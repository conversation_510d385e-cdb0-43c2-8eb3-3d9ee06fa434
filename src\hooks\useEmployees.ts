
import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Employee } from '@/types/employee';
import { useToast } from '@/hooks/use-toast';

export function useEmployees() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: employees = [], isLoading, error } = useQuery({
    queryKey: ['employees'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      return data.map((emp: any): Employee => ({
        id: emp.id,
        employeeId: emp.employee_id,
        firstName: emp.first_name,
        lastName: emp.last_name,
        email: emp.email,
        phone: emp.phone || '',
        role: emp.role,
        department: emp.department,
        position: emp.position,
        joinDate: emp.join_date,
        status: emp.status,
        shift: emp.shift,
        salary: emp.salary,
        avatar: emp.avatar,
        address: emp.address,
        emergencyContact: emp.emergency_contact,
        certifications: emp.certifications || [],
        skills: emp.skills || [],
        userId: emp.user_id, // Add the new user_id field
      }));
    },
  });

  const createEmployeeMutation = useMutation({
    mutationFn: async (employeeData: Omit<Employee, 'id'>) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('employees')
        .insert({
          employee_id: employeeData.employeeId,
          first_name: employeeData.firstName,
          last_name: employeeData.lastName,
          email: employeeData.email,
          phone: employeeData.phone,
          role: employeeData.role,
          department: employeeData.department,
          position: employeeData.position,
          join_date: employeeData.joinDate,
          status: employeeData.status,
          shift: employeeData.shift,
          salary: employeeData.salary,
          avatar: employeeData.avatar,
          address: employeeData.address,
          emergency_contact: employeeData.emergencyContact ? JSON.parse(JSON.stringify(employeeData.emergencyContact)) : null,
          certifications: employeeData.certifications,
          skills: employeeData.skills,
          created_by: user.id,
          user_id: employeeData.userId || null, // Include user_id in insert
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast({
        title: "Success",
        description: "Employee created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updateEmployeeMutation = useMutation({
    mutationFn: async ({ id, ...employeeData }: Employee) => {
      const { data, error } = await supabase
        .from('employees')
        .update({
          employee_id: employeeData.employeeId,
          first_name: employeeData.firstName,
          last_name: employeeData.lastName,
          email: employeeData.email,
          phone: employeeData.phone,
          role: employeeData.role,
          department: employeeData.department,
          position: employeeData.position,
          join_date: employeeData.joinDate,
          status: employeeData.status,
          shift: employeeData.shift,
          salary: employeeData.salary,
          avatar: employeeData.avatar,
          address: employeeData.address,
          emergency_contact: employeeData.emergencyContact ? JSON.parse(JSON.stringify(employeeData.emergencyContact)) : null,
          certifications: employeeData.certifications,
          skills: employeeData.skills,
          user_id: employeeData.userId || null, // Include user_id in update
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast({
        title: "Success",
        description: "Employee updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const deleteEmployeeMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('employees')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast({
        title: "Success",
        description: "Employee deleted successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return {
    employees,
    isLoading,
    error,
    createEmployee: createEmployeeMutation.mutate,
    updateEmployee: updateEmployeeMutation.mutate,
    deleteEmployee: deleteEmployeeMutation.mutate,
    isCreating: createEmployeeMutation.isPending,
    isUpdating: updateEmployeeMutation.isPending,
    isDeleting: deleteEmployeeMutation.isPending,
  };
}
