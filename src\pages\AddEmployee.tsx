
import { useNavigate } from 'react-router-dom';
import { useEmployees } from '@/hooks/useEmployees';
import EmployeeForm from '@/components/EmployeeForm';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export default function AddEmployee() {
  const navigate = useNavigate();
  const { createEmployee, isCreating } = useEmployees();

  const handleSubmit = (data: any) => {
    createEmployee({
      ...data,
      emergencyContact: null,
      certifications: [],
      skills: [],
    });
    navigate('/employees');
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={() => navigate('/employees')}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Kembali</span>
        </Button>
      </div>

      <EmployeeForm
        onSubmit={handleSubmit}
        onCancel={() => navigate('/employees')}
        isLoading={isCreating}
      />
    </div>
  );
}
