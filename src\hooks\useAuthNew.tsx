// New authentication hook to replace <PERSON><PERSON><PERSON> Auth
import { useState, useEffect, create<PERSON>ontext, useContext, ReactNode } from 'react';
import { signIn, signUp, getUserByToken, tokenStorage, AuthUser, Session } from '@/lib/auth';
import { EmployeeRole } from '@/types/employee';

interface AuthContextType {
  user: AuthUser | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, firstName?: string, lastName?: string, role?: string) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing session on mount
    const initializeAuth = async () => {
      try {
        const token = tokenStorage.get();
        if (token) {
          const userData = await getUserByToken(token);
          if (userData) {
            setUser(userData);
            setSession({
              access_token: token,
              user: userData,
              expires_at: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days
            });
          } else {
            // Token is invalid, remove it
            tokenStorage.remove();
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        tokenStorage.remove();
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const handleSignUp = async (
    email: string, 
    password: string, 
    firstName?: string, 
    lastName?: string, 
    role?: string
  ) => {
    try {
      const result = await signUp(email, password, firstName, lastName, role);
      if (result.error) {
        return { error: { message: result.error } };
      }
      
      // Note: In a real app, you might want to automatically sign in after signup
      // or require email verification first
      return { error: null };
    } catch (error) {
      console.error('Sign up error:', error);
      return { error: { message: 'An unexpected error occurred' } };
    }
  };

  const handleSignIn = async (email: string, password: string) => {
    try {
      const result = await signIn(email, password);
      if (result.error) {
        return { error: { message: result.error } };
      }

      if (result.session) {
        setUser(result.session.user);
        setSession(result.session);
        tokenStorage.set(result.session.access_token);
      }

      return { error: null };
    } catch (error) {
      console.error('Sign in error:', error);
      return { error: { message: 'An unexpected error occurred' } };
    }
  };

  const handleSignOut = async () => {
    try {
      setUser(null);
      setSession(null);
      tokenStorage.remove();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      session,
      loading,
      signUp: handleSignUp,
      signIn: handleSignIn,
      signOut: handleSignOut,
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Helper hook for role-based access
export function useRoleAccess() {
  const { user } = useAuth();

  const hasRole = (requiredRole: EmployeeRole): boolean => {
    return user?.profile?.role === requiredRole;
  };

  const hasAnyRole = (requiredRoles: EmployeeRole[]): boolean => {
    return requiredRoles.includes(user?.profile?.role as EmployeeRole);
  };

  const isAdmin = (): boolean => {
    return user?.profile?.role === 'admin';
  };

  const isManager = (): boolean => {
    return user?.profile?.role === 'manager';
  };

  return {
    profile: user?.profile,
    hasRole,
    hasAnyRole,
    isAdmin,
    isManager,
    isLoading: false, // Since we're not making async calls here
  };
}
