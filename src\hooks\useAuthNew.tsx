// Authentication hook using API endpoints
import { useState, useEffect, create<PERSON>ontext, useContext, ReactNode } from 'react';
import { authApi, tokenStorage } from '@/lib/api';
import { EmployeeRole } from '@/types/employee';

interface AuthUser {
  id: string;
  email: string;
  email_confirmed: boolean;
  profile?: {
    id: string;
    first_name?: string;
    last_name?: string;
    email?: string;
    role: EmployeeRole;
    created_at: Date;
    updated_at: Date;
  };
}

interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  signUp: (email: string, password: string, firstName?: string, lastName?: string, role?: string) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing session on mount
    const initializeAuth = async () => {
      try {
        const token = tokenStorage.get();
        if (token) {
          const response = await authApi.getCurrentUser();
          setUser(response.user);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        tokenStorage.remove();
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const handleSignUp = async (
    email: string,
    password: string,
    firstName?: string,
    lastName?: string,
    role?: string
  ) => {
    try {
      await authApi.register(email, password, firstName, lastName, role);
      return { error: null };
    } catch (error: any) {
      console.error('Sign up error:', error);
      return {
        error: {
          message: error.response?.data?.error || 'An unexpected error occurred'
        }
      };
    }
  };

  const handleSignIn = async (email: string, password: string) => {
    try {
      const response = await authApi.login(email, password);

      setUser(response.user);
      tokenStorage.set(response.access_token);

      return { error: null };
    } catch (error: any) {
      console.error('Sign in error:', error);
      return {
        error: {
          message: error.response?.data?.error || 'An unexpected error occurred'
        }
      };
    }
  };

  const handleSignOut = async () => {
    try {
      await authApi.logout();
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      setUser(null);
      tokenStorage.remove();
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      signUp: handleSignUp,
      signIn: handleSignIn,
      signOut: handleSignOut,
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Helper hook for role-based access
export function useRoleAccess() {
  const { user } = useAuth();

  const hasRole = (requiredRole: EmployeeRole): boolean => {
    return user?.profile?.role === requiredRole;
  };

  const hasAnyRole = (requiredRoles: EmployeeRole[]): boolean => {
    return requiredRoles.includes(user?.profile?.role as EmployeeRole);
  };

  const isAdmin = (): boolean => {
    return user?.profile?.role === 'admin';
  };

  const isManager = (): boolean => {
    return user?.profile?.role === 'manager';
  };

  return {
    profile: user?.profile,
    hasRole,
    hasAnyRole,
    isAdmin,
    isManager,
    isLoading: false,
  };
}
