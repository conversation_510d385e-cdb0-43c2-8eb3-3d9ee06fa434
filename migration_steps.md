# PostgreSQL Migration Steps

## Step 1: Install and Setup PostgreSQL

### Windows Installation
```bash
# Using Chocolatey
choco install postgresql

# Or download from: https://www.postgresql.org/download/windows/
```

### Create Database and User
```bash
# Connect to PostgreSQL as postgres user
psql -U postgres

# Create database
CREATE DATABASE manajemen_karyawan;

# Create user
CREATE USER app_user WITH PASSWORD 'your_secure_password';

# Grant privileges
GRANT ALL PRIVILEGES ON DATABASE manajemen_karyawan TO app_user;

# Connect to the new database
\c manajemen_karyawan

# Grant schema privileges
GRANT ALL ON SCHEMA public TO app_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO app_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO app_user;

# Exit psql
\q
```

## Step 2: Run Migration Scripts

### Apply Schema
```bash
# Run the schema migration
psql -U app_user -d manajemen_karyawan -f migration_schema.sql
```

### Extract and Migrate Data
1. **Extract data from your Supabase backup:**
   ```bash
   # Look for INSERT statements in your backup file
   grep -A 10 "INSERT INTO" db_manajemen_karyawan_backup.sql > extracted_data.sql
   ```

2. **Modify the extracted data:**
   - Replace `auth.users` references with `users` table
   - Update any Supabase-specific function calls
   - Ensure UUIDs are properly formatted

3. **Apply data migration:**
   ```bash
   psql -U app_user -d manajemen_karyawan -f migrate_data.sql
   ```

## Step 3: Install Dependencies

```bash
# Remove Supabase
npm uninstall @supabase/supabase-js

# Install PostgreSQL and auth dependencies
npm install pg @types/pg bcryptjs @types/bcryptjs jsonwebtoken @types/jsonwebtoken dotenv
```

## Step 4: Environment Configuration

Create `.env` file:
```env
VITE_DB_HOST=localhost
VITE_DB_PORT=5432
VITE_DB_NAME=manajemen_karyawan
VITE_DB_USER=app_user
VITE_DB_PASSWORD=your_secure_password
VITE_JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
VITE_JWT_EXPIRES_IN=7d
```

## Step 5: Update Application Code

### Replace Supabase Client
1. **Remove old Supabase integration:**
   ```bash
   rm -rf src/integrations/supabase/
   ```

2. **Update imports in all files:**
   - Replace `import { supabase } from '@/integrations/supabase/client'`
   - With appropriate service imports from `@/lib/services`

3. **Update authentication:**
   - Replace `useAuth` import to use new auth hook
   - Update `AuthProvider` in `App.tsx`

### Update Hooks
1. **Replace useAuth:**
   ```typescript
   // Old
   import { useAuth } from '@/hooks/useAuth';
   
   // New
   import { useAuth } from '@/hooks/useAuthNew';
   ```

2. **Replace data hooks:**
   ```typescript
   // Old
   import { useEmployees } from '@/hooks/useEmployees';
   
   // New
   import { useEmployees } from '@/hooks/useEmployeesNew';
   ```

## Step 6: Testing

### Database Connection Test
```bash
# Test database connection
psql -U app_user -d manajemen_karyawan -c "SELECT COUNT(*) FROM users;"
```

### Application Test
```bash
# Start development server
npm run dev

# Test authentication
# Test CRUD operations
# Test role-based access
```

## Step 7: Production Considerations

### Security
- Use environment variables for all sensitive data
- Implement proper password hashing (bcrypt)
- Use HTTPS in production
- Implement rate limiting for auth endpoints

### Performance
- Add database indexes for frequently queried columns
- Implement connection pooling
- Consider caching for read-heavy operations

### Backup
- Set up regular database backups
- Test backup restoration procedures
- Document recovery procedures

## Troubleshooting

### Common Issues
1. **Connection refused:** Check PostgreSQL service is running
2. **Permission denied:** Verify user privileges
3. **Module not found:** Ensure all dependencies are installed
4. **JWT errors:** Verify JWT_SECRET is set and sufficiently long

### Debug Commands
```bash
# Check PostgreSQL status
pg_ctl status

# View PostgreSQL logs
tail -f /var/log/postgresql/postgresql-*.log

# Test database connection
psql -U app_user -d manajemen_karyawan -c "SELECT version();"
```
