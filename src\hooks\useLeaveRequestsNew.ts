// Leave requests hook using API endpoints
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { leaveRequestApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

export function useLeaveRequests(params?: {
  employeeId?: string;
  status?: string;
}) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get all leave requests
  const { data: leaveRequestsData, isLoading, error } = useQuery({
    queryKey: ['leave-requests', params],
    queryFn: () => leaveRequestApi.getAll(params),
  });

  const leaveRequests = leaveRequestsData?.leaveRequests || [];

  // Create leave request mutation
  const createLeaveRequestMutation = useMutation({
    mutationFn: async (leaveRequestData: any) => {
      const response = await leaveRequestApi.create(leaveRequestData);
      return response.leaveRequest;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      toast({
        title: "Berhasil",
        description: "Permohonan cuti berhasil diajukan",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal mengajukan permohonan cuti",
        variant: "destructive",
      });
    },
  });

  // Update leave request mutation
  const updateLeaveRequestMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const response = await leaveRequestApi.update(id, data);
      return response.leaveRequest;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      toast({
        title: "Berhasil",
        description: "Permohonan cuti berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal memperbarui permohonan cuti",
        variant: "destructive",
      });
    },
  });

  // Delete leave request mutation
  const deleteLeaveRequestMutation = useMutation({
    mutationFn: async (id: string) => {
      await leaveRequestApi.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      toast({
        title: "Berhasil",
        description: "Permohonan cuti berhasil dihapus",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menghapus permohonan cuti",
        variant: "destructive",
      });
    },
  });

  // Approve leave request mutation
  const approveLeaveRequestMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await leaveRequestApi.approve(id);
      return response.leaveRequest;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      toast({
        title: "Berhasil",
        description: "Permohonan cuti berhasil disetujui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menyetujui permohonan cuti",
        variant: "destructive",
      });
    },
  });

  // Reject leave request mutation
  const rejectLeaveRequestMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await leaveRequestApi.reject(id);
      return response.leaveRequest;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      toast({
        title: "Berhasil",
        description: "Permohonan cuti berhasil ditolak",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menolak permohonan cuti",
        variant: "destructive",
      });
    },
  });

  return {
    leaveRequests,
    isLoading,
    error,
    createLeaveRequest: createLeaveRequestMutation.mutate,
    updateLeaveRequest: updateLeaveRequestMutation.mutate,
    deleteLeaveRequest: deleteLeaveRequestMutation.mutate,
    approveLeaveRequest: approveLeaveRequestMutation.mutate,
    rejectLeaveRequest: rejectLeaveRequestMutation.mutate,
    isCreating: createLeaveRequestMutation.isPending,
    isUpdating: updateLeaveRequestMutation.isPending,
    isDeleting: deleteLeaveRequestMutation.isPending,
    isApproving: approveLeaveRequestMutation.isPending,
    isRejecting: rejectLeaveRequestMutation.isPending,
  };
}

// Hook for getting a single leave request
export function useLeaveRequest(id: string) {
  return useQuery({
    queryKey: ['leave-request', id],
    queryFn: async () => {
      const response = await leaveRequestApi.getById(id);
      return response.leaveRequest;
    },
    enabled: !!id,
  });
}

// Hook for leave request statistics
export function useLeaveRequestStatistics() {
  return useQuery({
    queryKey: ['leave-request-statistics'],
    queryFn: async () => {
      return await leaveRequestApi.getStatistics();
    },
  });
}
