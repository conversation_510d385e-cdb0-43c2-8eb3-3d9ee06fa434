// API client for communicating with Express.js backend
import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
const API_PREFIX = '/api/v1';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}${API_PREFIX}`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // For cookies (refresh tokens)
});

// Token storage
export const tokenStorage = {
  get: (): string | null => {
    return localStorage.getItem('auth_token');
  },
  set: (token: string): void => {
    localStorage.setItem('auth_token', token);
  },
  remove: (): void => {
    localStorage.removeItem('auth_token');
  },
};

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = tokenStorage.get();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh token
        const refreshResponse = await api.post('/auth/refresh');
        const { access_token } = refreshResponse.data;
        
        tokenStorage.set(access_token);
        originalRequest.headers.Authorization = `Bearer ${access_token}`;
        
        return api(originalRequest);
      } catch (refreshError) {
        // Refresh failed, redirect to login
        tokenStorage.remove();
        window.location.href = '/auth';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// API response types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
}

export interface ApiError {
  error: string;
  details?: string;
  fields?: string[];
}

// Authentication API
export const authApi = {
  login: async (email: string, password: string): Promise<ApiResponse<{
    user: any;
    access_token: string;
    expires_in: number;
  }>> => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  register: async (
    email: string,
    password: string,
    firstName?: string,
    lastName?: string,
    role?: string
  ): Promise<ApiResponse<{ user: any }>> => {
    const response = await api.post('/auth/register', {
      email,
      password,
      firstName,
      lastName,
      role,
    });
    return response.data;
  },

  logout: async (): Promise<ApiResponse> => {
    const response = await api.post('/auth/logout');
    return response.data;
  },

  getCurrentUser: async (): Promise<ApiResponse<{ user: any }>> => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  updateProfile: async (profileData: any): Promise<ApiResponse<{ profile: any }>> => {
    const response = await api.put('/auth/profile', profileData);
    return response.data;
  },

  changePassword: async (currentPassword: string, newPassword: string): Promise<ApiResponse> => {
    const response = await api.put('/auth/change-password', {
      currentPassword,
      newPassword,
    });
    return response.data;
  },

  verifyToken: async (): Promise<ApiResponse<{ valid: boolean; user: any }>> => {
    const response = await api.get('/auth/verify');
    return response.data;
  },
};

// Employee API
export const employeeApi = {
  getAll: async (params?: {
    department?: string;
    role?: string;
    search?: string;
  }): Promise<ApiResponse<{ employees: any[]; total: number }>> => {
    const response = await api.get('/employees', { params });
    return response.data;
  },

  getById: async (id: string): Promise<ApiResponse<{ employee: any }>> => {
    const response = await api.get(`/employees/${id}`);
    return response.data;
  },

  create: async (employeeData: any): Promise<ApiResponse<{ employee: any }>> => {
    const response = await api.post('/employees', employeeData);
    return response.data;
  },

  update: async (id: string, employeeData: any): Promise<ApiResponse<{ employee: any }>> => {
    const response = await api.put(`/employees/${id}`, employeeData);
    return response.data;
  },

  delete: async (id: string): Promise<ApiResponse> => {
    const response = await api.delete(`/employees/${id}`);
    return response.data;
  },

  getStatistics: async (): Promise<ApiResponse<any>> => {
    const response = await api.get('/employees/statistics');
    return response.data;
  },
};

// Schedule API
export const scheduleApi = {
  getAll: async (params?: {
    employeeId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<{ schedules: any[]; total: number }>> => {
    const response = await api.get('/schedules', { params });
    return response.data;
  },

  getById: async (id: string): Promise<ApiResponse<{ schedule: any }>> => {
    const response = await api.get(`/schedules/${id}`);
    return response.data;
  },

  create: async (scheduleData: any): Promise<ApiResponse<{ schedule: any }>> => {
    const response = await api.post('/schedules', scheduleData);
    return response.data;
  },

  update: async (id: string, scheduleData: any): Promise<ApiResponse<{ schedule: any }>> => {
    const response = await api.put(`/schedules/${id}`, scheduleData);
    return response.data;
  },

  delete: async (id: string): Promise<ApiResponse> => {
    const response = await api.delete(`/schedules/${id}`);
    return response.data;
  },

  getStatistics: async (params?: {
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<any>> => {
    const response = await api.get('/schedules/statistics', { params });
    return response.data;
  },
};

// Leave Request API
export const leaveRequestApi = {
  getAll: async (params?: {
    employeeId?: string;
    status?: string;
  }): Promise<ApiResponse<{ leaveRequests: any[]; total: number }>> => {
    const response = await api.get('/leave-requests', { params });
    return response.data;
  },

  getById: async (id: string): Promise<ApiResponse<{ leaveRequest: any }>> => {
    const response = await api.get(`/leave-requests/${id}`);
    return response.data;
  },

  create: async (leaveRequestData: any): Promise<ApiResponse<{ leaveRequest: any }>> => {
    const response = await api.post('/leave-requests', leaveRequestData);
    return response.data;
  },

  update: async (id: string, leaveRequestData: any): Promise<ApiResponse<{ leaveRequest: any }>> => {
    const response = await api.put(`/leave-requests/${id}`, leaveRequestData);
    return response.data;
  },

  delete: async (id: string): Promise<ApiResponse> => {
    const response = await api.delete(`/leave-requests/${id}`);
    return response.data;
  },

  approve: async (id: string): Promise<ApiResponse<{ leaveRequest: any }>> => {
    const response = await api.patch(`/leave-requests/${id}/approve`);
    return response.data;
  },

  reject: async (id: string): Promise<ApiResponse<{ leaveRequest: any }>> => {
    const response = await api.patch(`/leave-requests/${id}/reject`);
    return response.data;
  },

  getStatistics: async (): Promise<ApiResponse<any>> => {
    const response = await api.get('/leave-requests/statistics');
    return response.data;
  },
};

// Management API
export const managementApi = {
  // Departments
  getDepartments: async (): Promise<ApiResponse<{ departments: any[] }>> => {
    const response = await api.get('/management/departments');
    return response.data;
  },

  createDepartment: async (departmentData: any): Promise<ApiResponse<{ department: any }>> => {
    const response = await api.post('/management/departments', departmentData);
    return response.data;
  },

  updateDepartment: async (id: string, departmentData: any): Promise<ApiResponse<{ department: any }>> => {
    const response = await api.put(`/management/departments/${id}`, departmentData);
    return response.data;
  },

  deleteDepartment: async (id: string): Promise<ApiResponse> => {
    const response = await api.delete(`/management/departments/${id}`);
    return response.data;
  },

  // Roles
  getRoles: async (): Promise<ApiResponse<{ roles: any[] }>> => {
    const response = await api.get('/management/roles');
    return response.data;
  },

  createRole: async (roleData: any): Promise<ApiResponse<{ role: any }>> => {
    const response = await api.post('/management/roles', roleData);
    return response.data;
  },

  updateRole: async (id: string, roleData: any): Promise<ApiResponse<{ role: any }>> => {
    const response = await api.put(`/management/roles/${id}`, roleData);
    return response.data;
  },

  // Positions
  getPositions: async (): Promise<ApiResponse<{ positions: any[] }>> => {
    const response = await api.get('/management/positions');
    return response.data;
  },

  createPosition: async (positionData: any): Promise<ApiResponse<{ position: any }>> => {
    const response = await api.post('/management/positions', positionData);
    return response.data;
  },

  updatePosition: async (id: string, positionData: any): Promise<ApiResponse<{ position: any }>> => {
    const response = await api.put(`/management/positions/${id}`, positionData);
    return response.data;
  },
};

export default api;
