// Employees hook using API endpoints
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { employeeApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

export function useEmployees(params?: {
  department?: string;
  role?: string;
  search?: string;
}) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get all employees
  const { data: employeesData, isLoading, error } = useQuery({
    queryKey: ['employees', params],
    queryFn: () => employeeApi.getAll(params),
  });

  const employees = employeesData?.employees || [];

  // Create employee mutation
  const createEmployeeMutation = useMutation({
    mutationFn: async (employeeData: any) => {
      const response = await employeeApi.create(employeeData);
      return response.employee;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast({
        title: "Berhasil",
        description: "Karyawan berhasil ditambahkan",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menambahkan karyawan",
        variant: "destructive",
      });
    },
  });

  // Update employee mutation
  const updateEmployeeMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const response = await employeeApi.update(id, data);
      return response.employee;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast({
        title: "Berhasil",
        description: "Data karyawan berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal memperbarui data karyawan",
        variant: "destructive",
      });
    },
  });

  // Delete employee mutation
  const deleteEmployeeMutation = useMutation({
    mutationFn: async (id: string) => {
      await employeeApi.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast({
        title: "Berhasil",
        description: "Karyawan berhasil dihapus",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menghapus karyawan",
        variant: "destructive",
      });
    },
  });

  return {
    employees,
    isLoading,
    error,
    createEmployee: createEmployeeMutation.mutate,
    updateEmployee: updateEmployeeMutation.mutate,
    deleteEmployee: deleteEmployeeMutation.mutate,
    isCreating: createEmployeeMutation.isPending,
    isUpdating: updateEmployeeMutation.isPending,
    isDeleting: deleteEmployeeMutation.isPending,
  };
}

// Hook for getting a single employee
export function useEmployee(id: string) {
  return useQuery({
    queryKey: ['employee', id],
    queryFn: async () => {
      const response = await employeeApi.getById(id);
      return response.employee;
    },
    enabled: !!id,
  });
}

// Hook for employee statistics
export function useEmployeeStatistics() {
  return useQuery({
    queryKey: ['employee-statistics'],
    queryFn: async () => {
      return await employeeApi.getStatistics();
    },
  });
}
