
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Users, RotateCcw } from 'lucide-react';
import { useEmployees } from '@/hooks/useEmployeesNew';
import { useSchedules } from '@/hooks/useSchedules';
import { useToast } from '@/hooks/use-toast';

interface ShiftPattern {
  day: number;
  shift: 'morning' | 'afternoon' | 'night' | 'off';
  startTime: string;
  endTime: string;
  label: string;
}

const SHIFT_PATTERNS: ShiftPattern[] = [
  { day: 1, shift: 'morning', startTime: '08:00', endTime: '14:00', label: 'P' },
  { day: 2, shift: 'morning', startTime: '08:00', endTime: '14:00', label: 'P' },
  { day: 3, shift: 'afternoon', startTime: '14:00', endTime: '20:00', label: 'S' },
  { day: 4, shift: 'afternoon', startTime: '14:00', endTime: '20:00', label: 'S' },
  { day: 5, shift: 'night', startTime: '20:00', endTime: '08:00', label: 'M' },
  { day: 6, shift: 'night', startTime: '20:00', endTime: '08:00', label: 'M' },
  { day: 7, shift: 'off', startTime: '', endTime: '', label: 'L' },
  { day: 8, shift: 'off', startTime: '', endTime: '', label: 'L' },
];

const ROTATION_OFFSETS = {
  morning: 0,   // P-P-S-S-M-M-L-L
  afternoon: 2, // S-S-M-M-L-L-P-P  
  night: 4,     // M-M-L-L-P-P-S-S
};

export function RotatingShiftGenerator() {
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
  const [startDate, setStartDate] = useState('');
  const [weeksToGenerate, setWeeksToGenerate] = useState(4);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  
  const { employees } = useEmployees();
  const { createSchedule, isCreatingSchedule } = useSchedules();
  const { toast } = useToast();

  // Filter perawat saja
  const nurses = employees.filter(emp => emp.role === 'nurse' || emp.role === 'doctor');

  const generateRotatingSchedule = () => {
    if (!startDate || selectedEmployees.length === 0) {
      toast({
        title: "Error",
        description: "Pilih tanggal mulai dan minimal 1 perawat",
        variant: "destructive",
      });
      return;
    }

    const schedules: any[] = [];
    const startDateObj = new Date(startDate);
    const totalDays = weeksToGenerate * 7;

    selectedEmployees.forEach((employeeId, employeeIndex) => {
      const employee = employees.find(emp => emp.id === employeeId);
      if (!employee) return;

      // Tentukan rotation offset berdasarkan urutan perawat
      const rotationKeys = Object.keys(ROTATION_OFFSETS) as Array<keyof typeof ROTATION_OFFSETS>;
      const rotationType = rotationKeys[employeeIndex % rotationKeys.length];
      const offset = ROTATION_OFFSETS[rotationType];

      for (let dayIndex = 0; dayIndex < totalDays; dayIndex++) {
        const currentDate = new Date(startDateObj);
        currentDate.setDate(startDateObj.getDate() + dayIndex);
        
        // Hitung posisi dalam siklus 8 hari dengan offset
        const cyclePosition = (dayIndex + offset) % 8;
        const pattern = SHIFT_PATTERNS[cyclePosition];

        if (pattern.shift !== 'off') {
          schedules.push({
            employeeId: employeeId,
            employeeName: `${employee.firstName} ${employee.lastName}`,
            shiftDate: currentDate.toISOString().split('T')[0],
            shiftType: pattern.shift,
            startTime: pattern.startTime,
            endTime: pattern.endTime,
            status: 'scheduled' as const,
            notes: `Rotating shift - Day ${cyclePosition + 1} of 8`,
            cycleDay: cyclePosition + 1,
            rotationType: rotationType,
          });
        }
      }
    });

    setPreviewData(schedules);
  };

  const saveSchedules = async () => {
    setIsGenerating(true);
    try {
      for (const schedule of previewData) {
        await new Promise(resolve => {
          createSchedule({
            employeeId: schedule.employeeId,
            shiftDate: schedule.shiftDate,
            shiftType: schedule.shiftType,
            startTime: schedule.startTime,
            endTime: schedule.endTime,
            status: schedule.status,
            notes: schedule.notes,
          });
          setTimeout(resolve, 100); // Small delay to avoid overwhelming the API
        });
      }
      
      toast({
        title: "Berhasil",
        description: `${previewData.length} jadwal rotating shift berhasil dibuat`,
      });
      
      setPreviewData([]);
      setSelectedEmployees([]);
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal menyimpan jadwal",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const getShiftColor = (shiftType: string) => {
    switch (shiftType) {
      case 'morning': return 'bg-yellow-100 text-yellow-800';
      case 'afternoon': return 'bg-blue-100 text-blue-800';
      case 'night': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getShiftLabel = (shiftType: string) => {
    switch (shiftType) {
      case 'morning': return 'Pagi';
      case 'afternoon': return 'Siang';
      case 'night': return 'Malam';
      default: return '';
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <RotateCcw className="h-5 w-5" />
            <span>Generator Rotating Shift</span>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Sistem rotasi 8 hari: 2 hari pagi → 2 hari siang → 2 hari malam → 2 hari libur
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Tanggal Mulai</Label>
              <Input
                id="startDate"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="weeks">Jumlah Minggu</Label>
              <Select value={weeksToGenerate.toString()} onValueChange={(value) => setWeeksToGenerate(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="2">2 Minggu</SelectItem>
                  <SelectItem value="4">4 Minggu</SelectItem>
                  <SelectItem value="8">8 Minggu</SelectItem>
                  <SelectItem value="12">12 Minggu</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Perawat/Dokter</Label>
              <Select onValueChange={(value) => {
                if (!selectedEmployees.includes(value)) {
                  setSelectedEmployees([...selectedEmployees, value]);
                }
              }}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih perawat/dokter" />
                </SelectTrigger>
                <SelectContent>
                  {nurses.map((nurse) => (
                    <SelectItem key={nurse.id} value={nurse.id}>
                      {nurse.firstName} {nurse.lastName} - {nurse.department}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {selectedEmployees.length > 0 && (
            <div className="space-y-2">
              <Label>Perawat Terpilih:</Label>
              <div className="flex flex-wrap gap-2">
                {selectedEmployees.map((empId) => {
                  const emp = employees.find(e => e.id === empId);
                  const rotationKeys = Object.keys(ROTATION_OFFSETS);
                  const rotationType = rotationKeys[selectedEmployees.indexOf(empId) % rotationKeys.length];
                  
                  return (
                    <Badge key={empId} variant="secondary" className="flex items-center space-x-2">
                      <span>{emp?.firstName} {emp?.lastName}</span>
                      <span className="text-xs">({rotationType})</span>
                      <button
                        onClick={() => setSelectedEmployees(selectedEmployees.filter(id => id !== empId))}
                        className="ml-2 text-red-500 hover:text-red-700"
                      >
                        ×
                      </button>
                    </Badge>
                  );
                })}
              </div>
            </div>
          )}

          <div className="flex space-x-2">
            <Button onClick={generateRotatingSchedule} disabled={!startDate || selectedEmployees.length === 0}>
              <Calendar className="h-4 w-4 mr-2" />
              Preview Jadwal
            </Button>
            
            {previewData.length > 0 && (
              <Button onClick={saveSchedules} disabled={isGenerating || isCreatingSchedule}>
                <Users className="h-4 w-4 mr-2" />
                {isGenerating ? 'Menyimpan...' : `Simpan ${previewData.length} Jadwal`}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Pattern Reference */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Pola Rotasi 8 Hari</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-8 gap-2 text-center">
            {SHIFT_PATTERNS.map((pattern, index) => (
              <div key={index} className="space-y-1">
                <div className="text-xs font-medium text-gray-500">
                  Hari {pattern.day}
                </div>
                <Badge className={pattern.shift === 'off' ? 'bg-gray-100 text-gray-800' : getShiftColor(pattern.shift)}>
                  {pattern.label}
                </Badge>
                <div className="text-xs text-gray-600">
                  {pattern.shift === 'off' ? 'Libur' : `${pattern.startTime}-${pattern.endTime}`}
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4 space-y-2 text-sm text-gray-600">
            <p><strong>Keterangan:</strong></p>
            <p>P = Pagi (08:00-14:00), S = Siang (14:00-20:00), M = Malam (20:00-08:00), L = Libur</p>
            <p>Setiap perawat mengikuti rotasi berbeda untuk memastikan coverage 24/7</p>
          </div>
        </CardContent>
      </Card>

      {/* Preview Results */}
      {previewData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>Preview Jadwal ({previewData.length} jadwal)</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="max-h-96 overflow-y-auto">
              <div className="space-y-2">
                {previewData.slice(0, 20).map((schedule, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div>
                        <p className="font-medium">{schedule.employeeName}</p>
                        <p className="text-sm text-gray-600">
                          {new Date(schedule.shiftDate).toLocaleDateString('id-ID')}
                        </p>
                      </div>
                      <Badge className={getShiftColor(schedule.shiftType)}>
                        {getShiftLabel(schedule.shiftType)}
                      </Badge>
                      <div className="text-sm">
                        {schedule.startTime} - {schedule.endTime}
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      Siklus hari {schedule.cycleDay}/8 ({schedule.rotationType})
                    </div>
                  </div>
                ))}
                {previewData.length > 20 && (
                  <p className="text-center text-gray-500 text-sm">
                    ... dan {previewData.length - 20} jadwal lainnya
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
