
import { useAuth } from '@/hooks/useAuthNew';
import { supabase } from '@/integrations/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, Calendar, Shield, Activity, Heart, Stethoscope, Pill, Wrench, UserCheck, FileText } from 'lucide-react';

interface UserProfile {
  role: string;
  first_name: string;
  last_name: string;
}

export function RoleBasedDashboard() {
  const { user } = useAuth();

  const { data: profile, isLoading } = useQuery({
    queryKey: ['profile', user?.id],
    queryFn: async () => {
      if (!user) return null;
      
      const { data, error } = await supabase
        .from('profiles')
        .select('role, first_name, last_name')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      return data as UserProfile;
    },
    enabled: !!user,
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">Profile not found</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const roleConfig = {
    admin: {
      title: 'Administrator Dashboard',
      icon: Shield,
      color: 'bg-red-100 text-red-800',
      features: [
        { name: 'Kelola Karyawan', icon: Users, description: 'Tambah, edit, hapus data karyawan' },
        { name: 'Manajemen Sistem', icon: Shield, description: 'Kelola role, departemen, posisi' },
        { name: 'Laporan Lengkap', icon: FileText, description: 'Akses semua laporan sistem' },
        { name: 'Jadwal Global', icon: Calendar, description: 'Kelola jadwal semua karyawan' },
      ]
    },
    doctor: {
      title: 'Dokter Dashboard',
      icon: Stethoscope,
      color: 'bg-blue-100 text-blue-800',
      features: [
        { name: 'Jadwal Praktik', icon: Calendar, description: 'Lihat jadwal praktik Anda' },
        { name: 'Data Pasien', icon: Heart, description: 'Akses data pasien (coming soon)' },
        { name: 'Permohonan Cuti', icon: FileText, description: 'Ajukan cuti medis' },
        { name: 'Profil Dokter', icon: UserCheck, description: 'Kelola profil profesional' },
      ]
    },
    nurse: {
      title: 'Perawat Dashboard',
      icon: Heart,
      color: 'bg-green-100 text-green-800',
      features: [
        { name: 'Jadwal Shift', icon: Calendar, description: 'Lihat jadwal shift Anda' },
        { name: 'Aktivitas Pasien', icon: Activity, description: 'Monitor aktivitas pasien' },
        { name: 'Laporan Shift', icon: FileText, description: 'Buat laporan shift' },
        { name: 'Tim Keperawatan', icon: Users, description: 'Koordinasi dengan tim' },
      ]
    },
    pharmacist: {
      title: 'Apoteker Dashboard',
      icon: Pill,
      color: 'bg-purple-100 text-purple-800',
      features: [
        { name: 'Inventori Obat', icon: Pill, description: 'Kelola stok obat (coming soon)' },
        { name: 'Resep Dokter', icon: FileText, description: 'Proses resep dokter' },
        { name: 'Jadwal Apotek', icon: Calendar, description: 'Lihat jadwal kerja apotek' },
        { name: 'Laporan Farmasi', icon: Activity, description: 'Laporan aktivitas farmasi' },
      ]
    },
    technician: {
      title: 'Teknisi Dashboard',
      icon: Wrench,
      color: 'bg-orange-100 text-orange-800',
      features: [
        { name: 'Jadwal Maintenance', icon: Calendar, description: 'Jadwal pemeliharaan alat' },
        { name: 'Alat Medis', icon: Wrench, description: 'Status alat medis (coming soon)' },
        { name: 'Laporan Teknis', icon: FileText, description: 'Laporan kerusakan & perbaikan' },
        { name: 'Inventori Alat', icon: Activity, description: 'Kelola inventori alat' },
      ]
    },
    receptionist: {
      title: 'Resepsionis Dashboard',
      icon: UserCheck,
      color: 'bg-pink-100 text-pink-800',
      features: [
        { name: 'Jadwal Kerja', icon: Calendar, description: 'Lihat jadwal kerja Anda' },
        { name: 'Registrasi Pasien', icon: Users, description: 'Daftar pasien baru (coming soon)' },
        { name: 'Antrian', icon: Activity, description: 'Kelola antrian pasien' },
        { name: 'Informasi Umum', icon: FileText, description: 'Akses informasi rumah sakit' },
      ]
    },
    manager: {
      title: 'Manajer Dashboard',
      icon: Users,
      color: 'bg-yellow-100 text-yellow-800',
      features: [
        { name: 'Tim Departemen', icon: Users, description: 'Kelola tim departemen' },
        { name: 'Jadwal Departemen', icon: Calendar, description: 'Koordinasi jadwal departemen' },
        { name: 'Laporan Kinerja', icon: FileText, description: 'Laporan kinerja tim' },
        { name: 'Evaluasi Karyawan', icon: Activity, description: 'Evaluasi kinerja karyawan' },
      ]
    }
  };

  const currentRole = profile.role as keyof typeof roleConfig;
  const config = roleConfig[currentRole] || roleConfig.nurse;
  const IconComponent = config.icon;

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{config.title}</h1>
          <p className="text-gray-600">
            Selamat datang, {profile.first_name} {profile.last_name}
          </p>
        </div>
        <Badge className={config.color}>
          <IconComponent className="w-4 h-4 mr-2" />
          {currentRole.charAt(0).toUpperCase() + currentRole.slice(1)}
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {config.features.map((feature, index) => {
          const FeatureIcon = feature.icon;
          return (
            <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <FeatureIcon className="w-5 h-5 text-primary" />
                  </div>
                  <CardTitle className="text-lg">{feature.name}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">{feature.description}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Akses Cepat Berdasarkan Role</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600">
            {currentRole === 'admin' && (
              <p>Sebagai Administrator, Anda memiliki akses penuh ke semua fitur sistem termasuk manajemen karyawan, role, dan laporan.</p>
            )}
            {currentRole === 'doctor' && (
              <p>Sebagai Dokter, Anda dapat mengakses jadwal praktik dan mengelola data medis pasien.</p>
            )}
            {currentRole === 'nurse' && (
              <p>Sebagai Perawat, Anda dapat melihat jadwal shift dan mengelola aktivitas keperawatan.</p>
            )}
            {currentRole === 'pharmacist' && (
              <p>Sebagai Apoteker, Anda dapat mengelola inventori obat dan memproses resep dokter.</p>
            )}
            {currentRole === 'technician' && (
              <p>Sebagai Teknisi, Anda dapat mengelola maintenance alat medis dan inventori peralatan.</p>
            )}
            {currentRole === 'receptionist' && (
              <p>Sebagai Resepsionis, Anda dapat mengelola registrasi pasien dan antrian.</p>
            )}
            {currentRole === 'manager' && (
              <p>Sebagai Manajer, Anda dapat mengelola tim departemen dan mengevaluasi kinerja karyawan.</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
