// Schedule service
import { query, transaction, Schedule } from '@/config/database';

export interface ScheduleWithEmployee extends Schedule {
  employee?: {
    first_name: string;
    last_name: string;
    department: string;
    position: string;
  };
}

export class ScheduleService {
  // Get all schedules with employee information
  static async getAll(): Promise<ScheduleWithEmployee[]> {
    try {
      const result = await query(`
        SELECT s.*, 
               e.first_name, e.last_name, e.department, e.position
        FROM schedules s
        INNER JOIN employees e ON s.employee_id = e.id
        ORDER BY s.shift_date ASC, s.start_time ASC
      `);

      return result.rows.map(row => ({
        id: row.id,
        employee_id: row.employee_id,
        shift_date: row.shift_date,
        shift_type: row.shift_type,
        start_time: row.start_time,
        end_time: row.end_time,
        status: row.status,
        notes: row.notes,
        created_by: row.created_by,
        created_at: row.created_at,
        updated_at: row.updated_at,
        employee: {
          first_name: row.first_name,
          last_name: row.last_name,
          department: row.department,
          position: row.position,
        }
      }));
    } catch (error) {
      console.error('Get all schedules error:', error);
      throw new Error('Failed to fetch schedules');
    }
  }

  // Get schedule by ID
  static async getById(id: string): Promise<ScheduleWithEmployee | null> {
    try {
      const result = await query(`
        SELECT s.*, 
               e.first_name, e.last_name, e.department, e.position
        FROM schedules s
        INNER JOIN employees e ON s.employee_id = e.id
        WHERE s.id = $1
      `, [id]);

      if (result.rows.length === 0) return null;

      const row = result.rows[0];
      return {
        id: row.id,
        employee_id: row.employee_id,
        shift_date: row.shift_date,
        shift_type: row.shift_type,
        start_time: row.start_time,
        end_time: row.end_time,
        status: row.status,
        notes: row.notes,
        created_by: row.created_by,
        created_at: row.created_at,
        updated_at: row.updated_at,
        employee: {
          first_name: row.first_name,
          last_name: row.last_name,
          department: row.department,
          position: row.position,
        }
      };
    } catch (error) {
      console.error('Get schedule by ID error:', error);
      throw new Error('Failed to fetch schedule');
    }
  }

  // Get schedules by employee ID
  static async getByEmployeeId(employeeId: string): Promise<ScheduleWithEmployee[]> {
    try {
      const result = await query(`
        SELECT s.*, 
               e.first_name, e.last_name, e.department, e.position
        FROM schedules s
        INNER JOIN employees e ON s.employee_id = e.id
        WHERE s.employee_id = $1
        ORDER BY s.shift_date ASC, s.start_time ASC
      `, [employeeId]);

      return result.rows.map(row => ({
        id: row.id,
        employee_id: row.employee_id,
        shift_date: row.shift_date,
        shift_type: row.shift_type,
        start_time: row.start_time,
        end_time: row.end_time,
        status: row.status,
        notes: row.notes,
        created_by: row.created_by,
        created_at: row.created_at,
        updated_at: row.updated_at,
        employee: {
          first_name: row.first_name,
          last_name: row.last_name,
          department: row.department,
          position: row.position,
        }
      }));
    } catch (error) {
      console.error('Get schedules by employee ID error:', error);
      throw new Error('Failed to fetch employee schedules');
    }
  }

  // Get schedules by date range
  static async getByDateRange(startDate: Date, endDate: Date): Promise<ScheduleWithEmployee[]> {
    try {
      const result = await query(`
        SELECT s.*, 
               e.first_name, e.last_name, e.department, e.position
        FROM schedules s
        INNER JOIN employees e ON s.employee_id = e.id
        WHERE s.shift_date >= $1 AND s.shift_date <= $2
        ORDER BY s.shift_date ASC, s.start_time ASC
      `, [startDate, endDate]);

      return result.rows.map(row => ({
        id: row.id,
        employee_id: row.employee_id,
        shift_date: row.shift_date,
        shift_type: row.shift_type,
        start_time: row.start_time,
        end_time: row.end_time,
        status: row.status,
        notes: row.notes,
        created_by: row.created_by,
        created_at: row.created_at,
        updated_at: row.updated_at,
        employee: {
          first_name: row.first_name,
          last_name: row.last_name,
          department: row.department,
          position: row.position,
        }
      }));
    } catch (error) {
      console.error('Get schedules by date range error:', error);
      throw new Error('Failed to fetch schedules by date range');
    }
  }

  // Create schedule
  static async create(scheduleData: Omit<Schedule, 'id' | 'created_at' | 'updated_at'>, createdBy: string): Promise<Schedule> {
    try {
      // Check if employee exists
      const employeeCheck = await query(
        'SELECT id FROM employees WHERE id = $1',
        [scheduleData.employee_id]
      );
      if (employeeCheck.rows.length === 0) {
        throw new Error('Employee not found');
      }

      // Check for schedule conflicts
      const conflictCheck = await query(`
        SELECT id FROM schedules 
        WHERE employee_id = $1 
        AND shift_date = $2 
        AND status != 'cancelled'
        AND (
          (start_time <= $3 AND end_time > $3) OR
          (start_time < $4 AND end_time >= $4) OR
          (start_time >= $3 AND end_time <= $4)
        )
      `, [scheduleData.employee_id, scheduleData.shift_date, scheduleData.start_time, scheduleData.end_time]);

      if (conflictCheck.rows.length > 0) {
        throw new Error('Schedule conflict detected for this employee');
      }

      const result = await query(`
        INSERT INTO schedules (
          employee_id, shift_date, shift_type, start_time, 
          end_time, status, notes, created_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `, [
        scheduleData.employee_id,
        scheduleData.shift_date,
        scheduleData.shift_type,
        scheduleData.start_time,
        scheduleData.end_time,
        scheduleData.status || 'scheduled',
        scheduleData.notes,
        createdBy
      ]);

      return result.rows[0];
    } catch (error) {
      console.error('Create schedule error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create schedule');
    }
  }

  // Update schedule
  static async update(id: string, scheduleData: Partial<Schedule>): Promise<Schedule> {
    try {
      // Remove fields that shouldn't be updated
      const { id: _, created_at, updated_at, created_by, ...updateData } = scheduleData;
      
      const fields = Object.keys(updateData);
      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      // Check if schedule exists
      const existingSchedule = await this.getById(id);
      if (!existingSchedule) {
        throw new Error('Schedule not found');
      }

      // If updating time or date, check for conflicts
      if (updateData.shift_date || updateData.start_time || updateData.end_time) {
        const checkDate = updateData.shift_date || existingSchedule.shift_date;
        const checkStartTime = updateData.start_time || existingSchedule.start_time;
        const checkEndTime = updateData.end_time || existingSchedule.end_time;

        const conflictCheck = await query(`
          SELECT id FROM schedules 
          WHERE employee_id = $1 
          AND shift_date = $2 
          AND id != $3
          AND status != 'cancelled'
          AND (
            (start_time <= $4 AND end_time > $4) OR
            (start_time < $5 AND end_time >= $5) OR
            (start_time >= $4 AND end_time <= $5)
          )
        `, [existingSchedule.employee_id, checkDate, id, checkStartTime, checkEndTime]);

        if (conflictCheck.rows.length > 0) {
          throw new Error('Schedule conflict detected for this employee');
        }
      }

      const values = fields.map(field => updateData[field as keyof Schedule]);
      const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

      const result = await query(`
        UPDATE schedules 
        SET ${setClause}, updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `, [id, ...values]);

      if (result.rows.length === 0) {
        throw new Error('Schedule not found');
      }

      return result.rows[0];
    } catch (error) {
      console.error('Update schedule error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update schedule');
    }
  }

  // Delete schedule
  static async delete(id: string): Promise<void> {
    try {
      const result = await query('DELETE FROM schedules WHERE id = $1', [id]);
      
      if (result.rowCount === 0) {
        throw new Error('Schedule not found');
      }
    } catch (error) {
      console.error('Delete schedule error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete schedule');
    }
  }

  // Get schedule statistics
  static async getStatistics(startDate?: Date, endDate?: Date): Promise<any> {
    try {
      let dateFilter = '';
      let params: any[] = [];

      if (startDate && endDate) {
        dateFilter = 'WHERE shift_date >= $1 AND shift_date <= $2';
        params = [startDate, endDate];
      }

      const totalResult = await query(`SELECT COUNT(*) as total FROM schedules ${dateFilter}`, params);
      const statusResult = await query(`
        SELECT status, COUNT(*) as count 
        FROM schedules ${dateFilter}
        GROUP BY status 
        ORDER BY count DESC
      `, params);
      const typeResult = await query(`
        SELECT shift_type, COUNT(*) as count 
        FROM schedules ${dateFilter}
        GROUP BY shift_type 
        ORDER BY count DESC
      `, params);

      return {
        total: parseInt(totalResult.rows[0].total),
        byStatus: statusResult.rows,
        byType: typeResult.rows
      };
    } catch (error) {
      console.error('Get schedule statistics error:', error);
      throw new Error('Failed to get schedule statistics');
    }
  }
}
