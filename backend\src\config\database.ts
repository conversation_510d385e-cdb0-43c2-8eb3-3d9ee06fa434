// Database connection configuration
import { Pool, PoolClient } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'manajemen_karyawan',
  user: process.env.DB_USER || 'app_user',
  password: process.env.DB_PASSWORD || '',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};

// Create connection pool
export const pool = new Pool(dbConfig);

// Database query helper
export async function query(text: string, params?: any[]): Promise<any> {
  const client = await pool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
}

// Transaction helper
export async function transaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Test database connection
export async function testConnection(): Promise<boolean> {
  try {
    const result = await query('SELECT NOW()');
    console.log('✅ Database connected successfully at:', result.rows[0].now);
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// Graceful shutdown
export async function closeConnection(): Promise<void> {
  try {
    await pool.end();
    console.log('📦 Database connection pool closed');
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
}

// Database types
export interface User {
  id: string;
  email: string;
  password_hash: string;
  email_confirmed: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Profile {
  id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  role: 'admin' | 'doctor' | 'nurse' | 'pharmacist' | 'technician' | 'receptionist' | 'manager';
  created_at: Date;
  updated_at: Date;
}

export interface Employee {
  id: string;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'doctor' | 'nurse' | 'pharmacist' | 'technician' | 'receptionist' | 'manager';
  department: 'emergency' | 'surgery' | 'pediatrics' | 'cardiology' | 'orthopedics' | 'pharmacy' | 'laboratory' | 'radiology' | 'administration' | 'maintenance';
  position: string;
  join_date: Date;
  status: 'active' | 'inactive' | 'on_leave' | 'terminated';
  shift: 'morning' | 'afternoon' | 'night' | 'rotating' | 'regular';
  salary?: number;
  avatar?: string;
  address?: string;
  user_id?: string;
  created_by?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Schedule {
  id: string;
  employee_id: string;
  shift_date: Date;
  shift_type: 'morning' | 'afternoon' | 'night' | 'rotating' | 'regular';
  start_time: string;
  end_time: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
  notes?: string;
  created_by?: string;
  created_at: Date;
  updated_at: Date;
}

export interface LeaveRequest {
  id: string;
  employee_id: string;
  leave_type: string;
  start_date: Date;
  end_date: Date;
  reason?: string;
  status: string;
  approved_by?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Department {
  id: string;
  name: string;
  description?: string;
  manager_id?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions?: any;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface Position {
  id: string;
  name: string;
  description?: string;
  department_id?: string;
  role_id?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}
