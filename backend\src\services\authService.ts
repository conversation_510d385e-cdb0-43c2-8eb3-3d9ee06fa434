// Authentication service
import bcrypt from 'bcryptjs';
import { query, transaction, User, Profile } from '@/config/database';
import { generateToken, generateRefreshToken } from '@/middleware/auth';

const BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS || '12');

export interface AuthUser {
  id: string;
  email: string;
  email_confirmed: boolean;
  profile?: Profile;
}

export interface AuthResponse {
  user: AuthUser;
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export class AuthService {
  // Hash password
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, BCRYPT_ROUNDS);
  }

  // Verify password
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  // Register new user
  static async register(
    email: string,
    password: string,
    firstName?: string,
    lastName?: string,
    role?: string
  ): Promise<{ user?: AuthUser; error?: string }> {
    try {
      // Check if user already exists
      const existingUser = await query(
        'SELECT id FROM users WHERE email = $1',
        [email.toLowerCase()]
      );

      if (existingUser.rows.length > 0) {
        return { error: 'User with this email already exists' };
      }

      // Hash password
      const passwordHash = await this.hashPassword(password);

      // Create user and profile in transaction
      const result = await transaction(async (client) => {
        // Create user
        const userResult = await client.query(
          `INSERT INTO users (email, password_hash, email_confirmed, created_at, updated_at)
           VALUES ($1, $2, $3, NOW(), NOW())
           RETURNING id, email, email_confirmed, created_at, updated_at`,
          [email.toLowerCase(), passwordHash, false]
        );

        const user = userResult.rows[0];

        // Create profile
        const validRole = role || 'nurse';
        const profileResult = await client.query(
          `INSERT INTO profiles (id, first_name, last_name, email, role, created_at, updated_at)
           VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
           RETURNING *`,
          [user.id, firstName, lastName, email.toLowerCase(), validRole]
        );

        return {
          user,
          profile: profileResult.rows[0]
        };
      });

      const authUser: AuthUser = {
        id: result.user.id,
        email: result.user.email,
        email_confirmed: result.user.email_confirmed,
        profile: result.profile
      };

      return { user: authUser };
    } catch (error) {
      console.error('Registration error:', error);
      return { error: 'Failed to create user account' };
    }
  }

  // Login user
  static async login(
    email: string,
    password: string
  ): Promise<{ auth?: AuthResponse; error?: string }> {
    try {
      // Get user with profile
      const result = await query(
        `SELECT u.id, u.email, u.password_hash, u.email_confirmed,
                p.first_name, p.last_name, p.role, p.created_at as profile_created_at, p.updated_at as profile_updated_at
         FROM users u
         LEFT JOIN profiles p ON u.id = p.id
         WHERE u.email = $1`,
        [email.toLowerCase()]
      );

      if (result.rows.length === 0) {
        return { error: 'Invalid email or password' };
      }

      const user = result.rows[0];

      // Verify password
      const isValidPassword = await this.verifyPassword(password, user.password_hash);
      if (!isValidPassword) {
        return { error: 'Invalid email or password' };
      }

      // Generate tokens
      const accessToken = generateToken(user.id);
      const refreshToken = generateRefreshToken(user.id);
      const expiresIn = 7 * 24 * 60 * 60; // 7 days in seconds

      const authUser: AuthUser = {
        id: user.id,
        email: user.email,
        email_confirmed: user.email_confirmed,
        profile: {
          id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          email: user.email,
          role: user.role,
          created_at: user.profile_created_at,
          updated_at: user.profile_updated_at,
        },
      };

      const authResponse: AuthResponse = {
        user: authUser,
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: expiresIn,
      };

      return { auth: authResponse };
    } catch (error) {
      console.error('Login error:', error);
      return { error: 'Failed to authenticate user' };
    }
  }

  // Get user by ID
  static async getUserById(userId: string): Promise<AuthUser | null> {
    try {
      const result = await query(
        `SELECT u.id, u.email, u.email_confirmed,
                p.first_name, p.last_name, p.role, p.created_at as profile_created_at, p.updated_at as profile_updated_at
         FROM users u
         LEFT JOIN profiles p ON u.id = p.id
         WHERE u.id = $1`,
        [userId]
      );

      if (result.rows.length === 0) return null;

      const user = result.rows[0];
      return {
        id: user.id,
        email: user.email,
        email_confirmed: user.email_confirmed,
        profile: {
          id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          email: user.email,
          role: user.role,
          created_at: user.profile_created_at,
          updated_at: user.profile_updated_at,
        },
      };
    } catch (error) {
      console.error('Get user by ID error:', error);
      return null;
    }
  }

  // Update user profile
  static async updateProfile(
    userId: string,
    profileData: Partial<Profile>
  ): Promise<{ profile?: Profile; error?: string }> {
    try {
      const fields = Object.keys(profileData).filter(key => key !== 'id');
      if (fields.length === 0) {
        return { error: 'No fields to update' };
      }

      const values = fields.map(field => profileData[field as keyof Profile]);
      const setClause = fields.map((field, index) => `${field} = $${index + 2}`).join(', ');

      const result = await query(
        `UPDATE profiles 
         SET ${setClause}, updated_at = NOW()
         WHERE id = $1
         RETURNING *`,
        [userId, ...values]
      );

      if (result.rows.length === 0) {
        return { error: 'Profile not found' };
      }

      return { profile: result.rows[0] };
    } catch (error) {
      console.error('Update profile error:', error);
      return { error: 'Failed to update profile' };
    }
  }

  // Change password
  static async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<{ success?: boolean; error?: string }> {
    try {
      // Get current password hash
      const result = await query(
        'SELECT password_hash FROM users WHERE id = $1',
        [userId]
      );

      if (result.rows.length === 0) {
        return { error: 'User not found' };
      }

      const user = result.rows[0];

      // Verify current password
      const isValidPassword = await this.verifyPassword(currentPassword, user.password_hash);
      if (!isValidPassword) {
        return { error: 'Current password is incorrect' };
      }

      // Hash new password
      const newPasswordHash = await this.hashPassword(newPassword);

      // Update password
      await query(
        'UPDATE users SET password_hash = $1, updated_at = NOW() WHERE id = $2',
        [newPasswordHash, userId]
      );

      return { success: true };
    } catch (error) {
      console.error('Change password error:', error);
      return { error: 'Failed to change password' };
    }
  }
}
