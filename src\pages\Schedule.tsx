
import { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useSchedules } from '@/hooks/useSchedules';
import { useEmployees } from '@/hooks/useEmployees';
import { ScheduleHeader } from '@/components/Schedule/ScheduleHeader';
import { ScheduleStats } from '@/components/Schedule/ScheduleStats';
import { ScheduleList } from '@/components/Schedule/ScheduleList';
import { ScheduleCalendar } from '@/components/Schedule/ScheduleCalendar';
import { EmployeeScheduleView } from '@/components/Schedule/EmployeeScheduleView';
import { MonthlyScheduleView } from '@/components/Schedule/MonthlyScheduleView';
import { LeaveRequestsList } from '@/components/Schedule/LeaveRequestsList';
import { AdvancedScheduleManager } from '@/components/Schedule/AdvancedScheduleManager';

export default function Schedule() {
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false);
  const [isLeaveDialogO<PERSON>, setIsLeaveDialogOpen] = useState(false);
  const { schedules, leaveRequests, isLoadingSchedules, isLoadingLeaveRequests } = useSchedules();
  const { employees } = useEmployees();

  if (isLoadingSchedules || isLoadingLeaveRequests) {
    return (
      <div className="p-6 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <ScheduleHeader
        isScheduleDialogOpen={isScheduleDialogOpen}
        setIsScheduleDialogOpen={setIsScheduleDialogOpen}
        isLeaveDialogOpen={isLeaveDialogOpen}
        setIsLeaveDialogOpen={setIsLeaveDialogOpen}
      />

      <ScheduleStats 
        schedules={schedules}
        employees={employees}
        leaveRequests={leaveRequests}
      />

      <Tabs defaultValue="calendar" className="space-y-4">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="calendar">Kalender Jadwal</TabsTrigger>
          <TabsTrigger value="employee">Jadwal Karyawan</TabsTrigger>
          <TabsTrigger value="monthly">Jadwal Bulanan</TabsTrigger>
          <TabsTrigger value="schedules">Daftar Jadwal</TabsTrigger>
          <TabsTrigger value="leaves">Permohonan Cuti</TabsTrigger>
          <TabsTrigger value="rotating">Rotating Shift</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="calendar">
          <ScheduleCalendar schedules={schedules} />
        </TabsContent>

        <TabsContent value="employee">
          <EmployeeScheduleView schedules={schedules} />
        </TabsContent>

        <TabsContent value="monthly">
          <MonthlyScheduleView schedules={schedules} />
        </TabsContent>

        <TabsContent value="schedules">
          <ScheduleList schedules={schedules} />
        </TabsContent>

        <TabsContent value="leaves">
          <LeaveRequestsList leaveRequests={leaveRequests} />
        </TabsContent>

        <TabsContent value="rotating">
          <AdvancedScheduleManager />
        </TabsContent>

        <TabsContent value="advanced">
          <AdvancedScheduleManager />
        </TabsContent>
      </Tabs>
    </div>
  );
}
