--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.5

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: pg_graphql; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_graphql WITH SCHEMA graphql;


--
-- Name: EXTENSION pg_graphql; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_graphql IS 'pg_graphql: GraphQL support';


--
-- Name: pg_stat_statements; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA extensions;


--
-- Name: EXTENSION pg_stat_statements; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_stat_statements IS 'track planning and execution statistics of all SQL statements executed';


--
-- Name: pgcrypto; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA extensions;


--
-- Name: EXTENSION pgcrypto; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pgcrypto IS 'cryptographic functions';


--
-- Name: supabase_vault; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS supabase_vault WITH SCHEMA vault;


--
-- Name: EXTENSION supabase_vault; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION supabase_vault IS 'Supabase Vault Extension';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA extensions;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: department_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.department_type AS ENUM (
    'emergency',
    'surgery',
    'pediatrics',
    'cardiology',
    'orthopedics',
    'pharmacy',
    'laboratory',
    'radiology',
    'administration',
    'maintenance'
);


--
-- Name: employee_role; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.employee_role AS ENUM (
    'admin',
    'doctor',
    'nurse',
    'pharmacist',
    'technician',
    'receptionist',
    'manager'
);


--
-- Name: employee_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.employee_status AS ENUM (
    'active',
    'inactive',
    'on_leave',
    'terminated'
);


--
-- Name: shift_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.shift_status AS ENUM (
    'scheduled',
    'completed',
    'cancelled',
    'no_show'
);


--
-- Name: shift_type; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.shift_type AS ENUM (
    'morning',
    'afternoon',
    'night',
    'rotating',
    'regular'
);


--
-- Name: handle_new_user(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.handle_new_user() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$

BEGIN

  INSERT INTO public.profiles (id, first_name, last_name, email, role)

  VALUES (

    NEW.id,

    NEW.raw_user_meta_data ->> 'first_name',

    NEW.raw_user_meta_data ->> 'last_name',

    NEW.email,

    COALESCE(

      (NEW.raw_user_meta_data ->> 'role')::employee_role,

      'nurse'::employee_role

    )

  );

  RETURN NEW;

END;

$$;


--
-- Name: sync_employee_profile(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.sync_employee_profile() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    SET search_path TO ''
    AS $$

BEGIN

  UPDATE public.employees 

  SET user_id = NEW.id

  WHERE email = NEW.email AND user_id IS NULL;

  

  RETURN NEW;

END;

$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: departments; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.departments (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    description text,
    manager_id uuid,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: employees; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.employees (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    employee_id text NOT NULL,
    first_name text NOT NULL,
    last_name text NOT NULL,
    email text NOT NULL,
    phone text,
    role public.employee_role NOT NULL,
    department public.department_type NOT NULL,
    "position" text NOT NULL,
    join_date date NOT NULL,
    status public.employee_status DEFAULT 'active'::public.employee_status NOT NULL,
    shift public.shift_type NOT NULL,
    salary numeric(12,2),
    avatar text,
    address text,
    emergency_contact jsonb,
    certifications text[],
    skills text[],
    user_id uuid,
    created_by uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: leave_requests; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.leave_requests (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    employee_id uuid NOT NULL,
    leave_type text NOT NULL,
    start_date date NOT NULL,
    end_date date NOT NULL,
    reason text,
    status text DEFAULT 'pending'::text NOT NULL,
    approved_by uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: positions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.positions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    description text,
    department_id uuid,
    role_id uuid,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: profiles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.profiles (
    id uuid NOT NULL,
    first_name text,
    last_name text,
    email text,
    role public.employee_role DEFAULT 'nurse'::public.employee_role,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.roles (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name text NOT NULL,
    description text,
    permissions jsonb,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: schedules; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.schedules (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    employee_id uuid NOT NULL,
    shift_date date NOT NULL,
    shift_type public.shift_type NOT NULL,
    start_time time without time zone NOT NULL,
    end_time time without time zone NOT NULL,
    status public.shift_status DEFAULT 'scheduled'::public.shift_status NOT NULL,
    notes text,
    created_by uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Data for Name: departments; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.departments (id, name, description, manager_id, is_active, created_at, updated_at) FROM stdin;
84086e10-2164-467b-941b-354adf9b8aa6	emergency	Emergency Department	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
ee0d9cdf-1d07-448f-809b-4855248b85ac	surgery	Surgery Department	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
f78f2852-74aa-49aa-8aab-2d58e6b21773	pediatrics	Pediatrics Department	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
e4b2077c-b2ad-4e10-b663-f1d215824d61	cardiology	Cardiology Department	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
809221ca-a827-43ab-aebf-afb58ae7bcf7	orthopedics	Orthopedics Department	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
d5c1a425-7be4-4883-ab4c-afb462934c12	pharmacy	Pharmacy Department	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
71d3a696-b1f8-45ef-a9fd-a394cd830444	laboratory	Laboratory Department	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
5548a283-38ab-4f62-beaa-e9a5ab3d8efd	radiology	Radiology Department	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
05cd67ce-85de-427a-a9e7-82a51caf8adf	administration	Administration Department	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
dbfa0a9c-74d0-40b4-b149-91b52dcaec66	maintenance	Maintenance Department	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
\.


--
-- Data for Name: employees; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.employees (id, employee_id, first_name, last_name, email, phone, role, department, "position", join_date, status, shift, salary, avatar, address, emergency_contact, certifications, skills, user_id, created_by, created_at, updated_at) FROM stdin;
929751c5-7713-4375-8c7c-4438ecaa7d22	NUR001	Maria	Santos	<EMAIL>	+62 813-4567-8901	nurse	emergency	Head Nurse	2019-03-20	active	night	8000000.00	\N	\N	\N	{BLS,ACLS}	{"Patient Care","Emergency Response"}	\N	\N	2025-07-09 01:17:27.209931+00	2025-07-09 01:17:27.209931+00
5894ab33-ad3c-481e-a8b0-e823bc077c89	DOC001	Dr. Sarah	Johnson	<EMAIL>	+62 812-3456-7890	doctor	cardiology	Cardiologist	2020-01-15	active	morning	25000000.00	\N	\N	\N	{"Cardiology Specialist","Emergency Medicine"}	{"ECG Interpretation","Cardiac Catheterization"}	086c5a1b-59da-40aa-a95d-5a6166d1780d	\N	2025-07-09 01:17:27.209931+00	2025-07-09 01:17:27.209931+00
574a4a90-d452-4321-a7d9-4fc9012655be	PHA001	Ahmad	Rizki	<EMAIL>	+62 814-5678-9012	pharmacist	pharmacy	Senior Pharmacist	2021-06-10	active	morning	12000000.00	\N	\N	\N	{"Pharmacy License","Clinical Pharmacy"}	{"Drug Dispensing","Patient Counseling"}	bd61ae35-a7a3-4a2c-9f67-a2f55911f618	\N	2025-07-09 01:17:27.209931+00	2025-07-09 01:17:27.209931+00
\.


--
-- Data for Name: leave_requests; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.leave_requests (id, employee_id, leave_type, start_date, end_date, reason, status, approved_by, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: positions; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.positions (id, name, description, department_id, role_id, is_active, created_at, updated_at) FROM stdin;
b7e73004-7ae9-4d05-a311-f534b6408184	Head of emergency	Department head for emergency	84086e10-2164-467b-941b-354adf9b8aa6	e18930a6-fa6c-47c6-a14d-342fa3e9ad72	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
1ebc79a3-7fcd-4573-a62f-4630419d2cd9	Head of surgery	Department head for surgery	ee0d9cdf-1d07-448f-809b-4855248b85ac	e18930a6-fa6c-47c6-a14d-342fa3e9ad72	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
b4b60e2f-09da-4244-bc86-442b76e61639	Head of pediatrics	Department head for pediatrics	f78f2852-74aa-49aa-8aab-2d58e6b21773	e18930a6-fa6c-47c6-a14d-342fa3e9ad72	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
00e66e39-886f-45af-a0ba-ecab2d0f3d64	Head of cardiology	Department head for cardiology	e4b2077c-b2ad-4e10-b663-f1d215824d61	e18930a6-fa6c-47c6-a14d-342fa3e9ad72	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
42c4a897-e63c-4dfa-aa24-0e798ceae3ed	Head of orthopedics	Department head for orthopedics	809221ca-a827-43ab-aebf-afb58ae7bcf7	e18930a6-fa6c-47c6-a14d-342fa3e9ad72	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
c13c9439-9c8d-4a49-8310-c2cd447c885b	Head of pharmacy	Department head for pharmacy	d5c1a425-7be4-4883-ab4c-afb462934c12	e18930a6-fa6c-47c6-a14d-342fa3e9ad72	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
b8c44f61-a1e1-44cc-bfe9-85e7f898d2d0	Head of laboratory	Department head for laboratory	71d3a696-b1f8-45ef-a9fd-a394cd830444	e18930a6-fa6c-47c6-a14d-342fa3e9ad72	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
729891e9-08f4-4e0a-aa8c-77cc7871539e	Head of radiology	Department head for radiology	5548a283-38ab-4f62-beaa-e9a5ab3d8efd	e18930a6-fa6c-47c6-a14d-342fa3e9ad72	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
4ed76df2-e29a-4fa3-b800-7465bd2e5c42	Head of administration	Department head for administration	05cd67ce-85de-427a-a9e7-82a51caf8adf	e18930a6-fa6c-47c6-a14d-342fa3e9ad72	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
314ce8d2-c4d5-4daf-8877-f65ff67b1fdb	Head of maintenance	Department head for maintenance	dbfa0a9c-74d0-40b4-b149-91b52dcaec66	e18930a6-fa6c-47c6-a14d-342fa3e9ad72	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
\.


--
-- Data for Name: profiles; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.profiles (id, first_name, last_name, email, role, created_at, updated_at) FROM stdin;
df0f0b7d-547e-4af8-b865-b0f2e36e8b36	widi	yanata	<EMAIL>	nurse	2025-07-09 02:26:00.145+00	2025-07-09 02:26:00.145+00
086c5a1b-59da-40aa-a95d-5a6166d1780d	Dr. Sarah	Johnson	<EMAIL>	doctor	2025-07-09 02:27:11.766+00	2025-07-09 02:27:11.766+00
bd61ae35-a7a3-4a2c-9f67-a2f55911f618	Ahmad	Rizki	<EMAIL>	pharmacist	2025-07-09 02:43:36.444+00	2025-07-09 02:43:36.444+00
79a5311d-4713-4071-beac-719e2f3f6b78	widi	yanata	<EMAIL>	admin	2025-07-09 02:18:14.457+00	2025-07-09 02:18:14.458+00
\.


--
-- Data for Name: roles; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.roles (id, name, description, permissions, is_active, created_at, updated_at) FROM stdin;
1bb80955-f316-4868-a396-89a1eae762fa	admin	System Administrator	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
df10ad27-07c5-4bad-ab7d-9bfec9fab464	doctor	Medical Doctor	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
9a212155-8059-430b-968b-431b9ab1bdd2	nurse	Registered Nurse	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
4d38af45-dc92-4b51-a58d-db298b8404c6	pharmacist	Licensed Pharmacist	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
741a4ec3-c31e-4f6e-aead-365866b3cb85	technician	Medical Technician	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
57e7141e-d3cc-4b00-b14f-cdc94b4720f8	receptionist	Front Desk Receptionist	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
e18930a6-fa6c-47c6-a14d-342fa3e9ad72	manager	Department Manager	\N	t	2025-07-09 02:33:30.181619+00	2025-07-09 02:33:30.181619+00
\.


--
-- Data for Name: schedules; Type: TABLE DATA; Schema: public; Owner: -
--

COPY public.schedules (id, employee_id, shift_date, shift_type, start_time, end_time, status, notes, created_by, created_at, updated_at) FROM stdin;
040143de-34b3-41ec-8bfd-10061041045c	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-01	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 1 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:22.596163+00	2025-07-09 03:19:22.596163+00
a469ade5-0c31-4cea-89f4-fdca12eb1774	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-02	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 2 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:22.606266+00	2025-07-09 03:19:22.606266+00
dd23214e-ea9f-48d2-87b2-0b6cd99dac21	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-03	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 3 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:22.682437+00	2025-07-09 03:19:22.682437+00
9b225940-e376-4e5a-bac3-a11367a0e1ec	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-04	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 4 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:22.790577+00	2025-07-09 03:19:22.790577+00
ebf46ea4-3523-46aa-84ba-cd6b4662ed17	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-05	night	20:00:00	08:00:00	scheduled	Rotating shift - Day 5 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:22.897403+00	2025-07-09 03:19:22.897403+00
125ce0c3-b291-42cc-9cdd-c80a4100b95b	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-06	night	20:00:00	08:00:00	scheduled	Rotating shift - Day 6 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:22.996706+00	2025-07-09 03:19:22.996706+00
80607ee2-33b5-4a12-a713-8b78a4b47320	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-10	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 2 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:23.199811+00	2025-07-09 03:19:23.199811+00
c2a32316-58a1-405d-8609-129dff9e86e5	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-11	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 3 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:23.306679+00	2025-07-09 03:19:23.306679+00
1a733e66-979a-4892-a6ca-267fc7a71d0d	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-12	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 4 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:23.421546+00	2025-07-09 03:19:23.421546+00
3ae8c89c-20a4-4d8f-b767-0cf25dc4d293	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-13	night	20:00:00	08:00:00	scheduled	Rotating shift - Day 5 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:23.496272+00	2025-07-09 03:19:23.496272+00
5976c10f-6a16-433e-841e-b82fdf89e05e	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-14	night	20:00:00	08:00:00	scheduled	Rotating shift - Day 6 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:23.628035+00	2025-07-09 03:19:23.628035+00
b18bd9c0-41ff-4ca0-8fcb-45ca11b9d95a	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-17	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 1 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:23.70924+00	2025-07-09 03:19:23.70924+00
8c4dfe66-f27c-4c32-b338-edc1949bb32c	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-18	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 2 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:23.829892+00	2025-07-09 03:19:23.829892+00
6860980c-13a7-4647-9ba2-94becd89ec80	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-19	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 3 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:23.930299+00	2025-07-09 03:19:23.930299+00
8ca1da54-0136-4aac-b4c4-3011567e861e	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-20	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 4 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:24.027535+00	2025-07-09 03:19:24.027535+00
8c64f5f6-e243-40fe-b313-f6b4bed2ca0d	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-21	night	20:00:00	08:00:00	scheduled	Rotating shift - Day 5 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:24.134821+00	2025-07-09 03:19:24.134821+00
37cd4282-3215-4da6-94dc-36608608fd1e	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-22	night	20:00:00	08:00:00	scheduled	Rotating shift - Day 6 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:24.232855+00	2025-07-09 03:19:24.232855+00
14ba7006-a75e-4175-aa35-87d40b681126	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-25	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 1 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:24.336637+00	2025-07-09 03:19:24.336637+00
d2d9a7ce-0527-4734-beec-0640f447dcd6	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-26	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 2 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:24.437992+00	2025-07-09 03:19:24.437992+00
92e9b15e-a246-41e6-82cd-2133a354c732	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-27	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 3 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:24.542417+00	2025-07-09 03:19:24.542417+00
2c1dda27-aeb1-4ac3-8f61-15cd94063fab	929751c5-7713-4375-8c7c-4438ecaa7d22	2025-07-28	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 4 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:19:24.649719+00	2025-07-09 03:19:24.649719+00
0d96bcb7-ed72-4668-ab61-736152da33c0	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-04	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 2 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:58.507809+00	2025-07-09 03:20:58.507809+00
438ab2fe-6bf4-4d94-abd2-d6fda6140229	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-05	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 3 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:58.579904+00	2025-07-09 03:20:58.579904+00
04e4960e-573f-4a7d-9c88-7936cbc1b2e5	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-06	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 4 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:58.59721+00	2025-07-09 03:20:58.59721+00
c7bc3db3-bf46-4e1f-aba0-21424d2a068f	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-07	night	20:00:00	08:00:00	scheduled	Rotating shift - Day 5 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:58.664021+00	2025-07-09 03:20:58.664021+00
d430ae45-a63e-4b49-9b53-5c8068319c6e	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-08	night	20:00:00	08:00:00	scheduled	Rotating shift - Day 6 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:58.732825+00	2025-07-09 03:20:58.732825+00
501ac862-46c5-4726-965c-3c42d3b2bde1	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-11	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 1 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:58.816075+00	2025-07-09 03:20:58.816075+00
9724572e-3237-4411-8a2f-68edcb8afa2f	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-12	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 2 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:58.819858+00	2025-07-09 03:20:58.819858+00
69c3fe60-bc36-4144-b379-0995bb86958e	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-13	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 3 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:58.902985+00	2025-07-09 03:20:58.902985+00
ecf6f446-88e3-4478-8f5d-32d36e797098	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-14	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 4 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:58.98968+00	2025-07-09 03:20:58.98968+00
a0d921bc-e4a5-47f6-b591-429224f4b594	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-15	night	20:00:00	08:00:00	scheduled	Rotating shift - Day 5 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:59.077066+00	2025-07-09 03:20:59.077066+00
0e166a46-3bbb-4a55-906f-e8ae1682cdad	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-03	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 1 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:59.150015+00	2025-07-09 03:20:59.150015+00
798905ca-725b-49b8-b88e-d2d9713cfdf6	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-19	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 1 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:59.151254+00	2025-07-09 03:20:59.151254+00
3f33dc77-2781-457b-8ad9-74095a8b0174	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-16	night	20:00:00	08:00:00	scheduled	Rotating shift - Day 6 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:59.1525+00	2025-07-09 03:20:59.1525+00
0e56dc2a-1697-4c54-a12e-e2888f98982d	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-20	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 2 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:59.241845+00	2025-07-09 03:20:59.241845+00
544b2540-83ca-44a3-b310-558c951e6abc	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-21	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 3 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:59.346378+00	2025-07-09 03:20:59.346378+00
89987e34-8ca9-4fc5-8d8e-a608ef18dfbb	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-22	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 4 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:59.449581+00	2025-07-09 03:20:59.449581+00
a7fbd45c-a113-4876-bdb5-bfa682ad68dd	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-23	night	20:00:00	08:00:00	scheduled	Rotating shift - Day 5 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:59.545687+00	2025-07-09 03:20:59.545687+00
b0ea83de-88da-4836-839f-767bc7738c61	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-24	night	20:00:00	08:00:00	scheduled	Rotating shift - Day 6 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:59.656643+00	2025-07-09 03:20:59.656643+00
a3da8da9-4c48-4312-9c3b-ba595322deda	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-27	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 1 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:59.755277+00	2025-07-09 03:20:59.755277+00
4438443d-6927-441e-a657-45f683e16035	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-28	morning	08:00:00	14:00:00	scheduled	Rotating shift - Day 2 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:59.855129+00	2025-07-09 03:20:59.855129+00
8410abb2-5d40-4efb-9827-804e1e2cc624	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-29	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 3 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:20:59.954009+00	2025-07-09 03:20:59.954009+00
d4af3772-29f7-47c0-b98e-f99e374a4f81	5894ab33-ad3c-481e-a8b0-e823bc077c89	2025-07-30	afternoon	14:00:00	20:00:00	scheduled	Rotating shift - Day 4 of 8	79a5311d-4713-4071-beac-719e2f3f6b78	2025-07-09 03:21:00.107107+00	2025-07-09 03:21:00.107107+00
\.


--
-- Name: departments departments_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.departments
    ADD CONSTRAINT departments_name_key UNIQUE (name);


--
-- Name: departments departments_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.departments
    ADD CONSTRAINT departments_pkey PRIMARY KEY (id);


--
-- Name: employees employees_email_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_email_key UNIQUE (email);


--
-- Name: employees employees_employee_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_employee_id_key UNIQUE (employee_id);


--
-- Name: employees employees_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_pkey PRIMARY KEY (id);


--
-- Name: leave_requests leave_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.leave_requests
    ADD CONSTRAINT leave_requests_pkey PRIMARY KEY (id);


--
-- Name: positions positions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.positions
    ADD CONSTRAINT positions_pkey PRIMARY KEY (id);


--
-- Name: profiles profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.profiles
    ADD CONSTRAINT profiles_pkey PRIMARY KEY (id);


--
-- Name: roles roles_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_name_key UNIQUE (name);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (id);


--
-- Name: schedules schedules_employee_id_shift_date_start_time_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schedules
    ADD CONSTRAINT schedules_employee_id_shift_date_start_time_key UNIQUE (employee_id, shift_date, start_time);


--
-- Name: schedules schedules_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schedules
    ADD CONSTRAINT schedules_pkey PRIMARY KEY (id);


--
-- Name: profiles on_profile_created; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER on_profile_created AFTER INSERT ON public.profiles FOR EACH ROW EXECUTE FUNCTION public.sync_employee_profile();


--
-- Name: departments departments_manager_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.departments
    ADD CONSTRAINT departments_manager_id_fkey FOREIGN KEY (manager_id) REFERENCES public.employees(id);


--
-- Name: employees employees_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id);


--
-- Name: employees employees_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;


--
-- Name: leave_requests leave_requests_approved_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.leave_requests
    ADD CONSTRAINT leave_requests_approved_by_fkey FOREIGN KEY (approved_by) REFERENCES auth.users(id);


--
-- Name: leave_requests leave_requests_employee_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.leave_requests
    ADD CONSTRAINT leave_requests_employee_id_fkey FOREIGN KEY (employee_id) REFERENCES public.employees(id) ON DELETE CASCADE;


--
-- Name: positions positions_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.positions
    ADD CONSTRAINT positions_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments(id);


--
-- Name: positions positions_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.positions
    ADD CONSTRAINT positions_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id);


--
-- Name: profiles profiles_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.profiles
    ADD CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: schedules schedules_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schedules
    ADD CONSTRAINT schedules_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id);


--
-- Name: schedules schedules_employee_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schedules
    ADD CONSTRAINT schedules_employee_id_fkey FOREIGN KEY (employee_id) REFERENCES public.employees(id) ON DELETE CASCADE;


--
-- Name: departments Admins can manage departments; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Admins can manage departments" ON public.departments USING ((EXISTS ( SELECT 1
   FROM public.profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = 'admin'::public.employee_role)))));


--
-- Name: positions Admins can manage positions; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Admins can manage positions" ON public.positions USING ((EXISTS ( SELECT 1
   FROM public.profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = 'admin'::public.employee_role)))));


--
-- Name: roles Admins can manage roles; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Admins can manage roles" ON public.roles USING ((EXISTS ( SELECT 1
   FROM public.profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = 'admin'::public.employee_role)))));


--
-- Name: departments Anyone can view active departments; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Anyone can view active departments" ON public.departments FOR SELECT USING ((is_active = true));


--
-- Name: positions Anyone can view active positions; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Anyone can view active positions" ON public.positions FOR SELECT USING ((is_active = true));


--
-- Name: roles Anyone can view active roles; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Anyone can view active roles" ON public.roles FOR SELECT USING ((is_active = true));


--
-- Name: employees Authenticated users can create employees; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can create employees" ON public.employees FOR INSERT TO authenticated WITH CHECK ((auth.uid() = created_by));


--
-- Name: leave_requests Authenticated users can create leave requests; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can create leave requests" ON public.leave_requests FOR INSERT TO authenticated WITH CHECK (true);


--
-- Name: schedules Authenticated users can create schedules; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can create schedules" ON public.schedules FOR INSERT TO authenticated WITH CHECK ((auth.uid() = created_by));


--
-- Name: employees Authenticated users can delete employees; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can delete employees" ON public.employees FOR DELETE TO authenticated USING (true);


--
-- Name: schedules Authenticated users can delete schedules; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can delete schedules" ON public.schedules FOR DELETE TO authenticated USING (true);


--
-- Name: employees Authenticated users can update employees; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can update employees" ON public.employees FOR UPDATE TO authenticated USING (true);


--
-- Name: leave_requests Authenticated users can update leave requests; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can update leave requests" ON public.leave_requests FOR UPDATE TO authenticated USING (true);


--
-- Name: schedules Authenticated users can update schedules; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can update schedules" ON public.schedules FOR UPDATE TO authenticated USING (true);


--
-- Name: employees Authenticated users can view employees; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can view employees" ON public.employees FOR SELECT TO authenticated USING (true);


--
-- Name: leave_requests Authenticated users can view leave requests; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can view leave requests" ON public.leave_requests FOR SELECT TO authenticated USING (true);


--
-- Name: schedules Authenticated users can view schedules; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can view schedules" ON public.schedules FOR SELECT TO authenticated USING (true);


--
-- Name: profiles Enable insert for authenticated users only; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Enable insert for authenticated users only" ON public.profiles FOR INSERT TO authenticated WITH CHECK (true);


--
-- Name: profiles Users can insert their own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can insert their own profile" ON public.profiles FOR INSERT WITH CHECK ((auth.uid() = id));


--
-- Name: profiles Users can update their own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can update their own profile" ON public.profiles FOR UPDATE USING ((auth.uid() = id));


--
-- Name: profiles Users can view their own profile; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can view their own profile" ON public.profiles FOR SELECT USING ((auth.uid() = id));


--
-- Name: departments; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;

--
-- Name: employees; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.employees ENABLE ROW LEVEL SECURITY;

--
-- Name: leave_requests; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.leave_requests ENABLE ROW LEVEL SECURITY;

--
-- Name: positions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.positions ENABLE ROW LEVEL SECURITY;

--
-- Name: profiles; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

--
-- Name: roles; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;

--
-- Name: schedules; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.schedules ENABLE ROW LEVEL SECURITY;

--
-- Name: supabase_realtime; Type: PUBLICATION; Schema: -; Owner: -
--

CREATE PUBLICATION supabase_realtime WITH (publish = 'insert, update, delete, truncate');


--
-- Name: issue_graphql_placeholder; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER issue_graphql_placeholder ON sql_drop
         WHEN TAG IN ('DROP EXTENSION')
   EXECUTE FUNCTION extensions.set_graphql_placeholder();


--
-- Name: issue_pg_cron_access; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER issue_pg_cron_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_cron_access();


--
-- Name: issue_pg_graphql_access; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER issue_pg_graphql_access ON ddl_command_end
         WHEN TAG IN ('CREATE FUNCTION')
   EXECUTE FUNCTION extensions.grant_pg_graphql_access();


--
-- Name: issue_pg_net_access; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER issue_pg_net_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_net_access();


--
-- Name: pgrst_ddl_watch; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER pgrst_ddl_watch ON ddl_command_end
   EXECUTE FUNCTION extensions.pgrst_ddl_watch();


--
-- Name: pgrst_drop_watch; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER pgrst_drop_watch ON sql_drop
   EXECUTE FUNCTION extensions.pgrst_drop_watch();


--
-- PostgreSQL database dump complete
--

