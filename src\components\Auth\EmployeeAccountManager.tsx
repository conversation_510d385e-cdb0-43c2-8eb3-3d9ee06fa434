
import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UserPlus, Users, Mail, Shield, Search, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { useEmployees } from '@/hooks/useEmployees';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { EmployeeRole } from '@/types/employee';

const roleLabels: Record<EmployeeRole, string> = {
  admin: 'Administrator',
  doctor: 'Do<PERSON>er',
  nurse: '<PERSON><PERSON><PERSON>',
  pharmacist: 'Apoteker',
  technician: 'Teknisi',
  receptionist: 'Resepsionis',
  manager: 'Manajer'
};

const roleColors: Record<EmployeeRole, string> = {
  admin: 'bg-red-100 text-red-800',
  doctor: 'bg-blue-100 text-blue-800',
  nurse: 'bg-green-100 text-green-800',
  pharmacist: 'bg-purple-100 text-purple-800',
  technician: 'bg-orange-100 text-orange-800',
  receptionist: 'bg-pink-100 text-pink-800',
  manager: 'bg-yellow-100 text-yellow-800'
};

export function EmployeeAccountManager() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<any>(null);
  const [password, setPassword] = useState('');
  const [selectedRole, setSelectedRole] = useState<EmployeeRole>('nurse');
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const { employees, updateEmployee } = useEmployees();
  const { signUp } = useAuth();
  const { toast } = useToast();

  const handleCreateAccount = async (employee: any) => {
    setLoading(true);
    try {
      const { error } = await signUp(
        employee.email,
        password,
        employee.firstName,
        employee.lastName,
        selectedRole
      );
      
      if (error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: `Akun berhasil dibuat untuk ${employee.firstName} ${employee.lastName} dengan role ${roleLabels[selectedRole]}`,
        });
        setIsDialogOpen(false);
        setPassword('');
        setSelectedEmployee(null);
        setSelectedRole('nurse');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal membuat akun karyawan",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredEmployees = employees.filter(employee =>
    `${employee.firstName} ${employee.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.department.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const employeesWithAccount = filteredEmployees.filter(emp => emp.userId);
  const employeesWithoutAccount = filteredEmployees.filter(emp => !emp.userId);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Manajemen Akun Karyawan</span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="text-sm space-y-1">
              <p className="text-gray-600">
                <span className="font-medium text-green-600">{employeesWithAccount.length}</span> karyawan sudah memiliki akun
              </p>
              <p className="text-gray-600">
                <span className="font-medium text-orange-600">{employeesWithoutAccount.length}</span> karyawan belum memiliki akun
              </p>
            </div>
            <div className="w-full sm:w-auto">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Cari karyawan..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-full sm:w-80"
                />
              </div>
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nama</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Departemen</TableHead>
                <TableHead>Status Akun</TableHead>
                <TableHead>Aksi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEmployees.map((employee) => (
                <TableRow key={employee.id}>
                  <TableCell className="font-medium">
                    {employee.firstName} {employee.lastName}
                  </TableCell>
                  <TableCell>{employee.email}</TableCell>
                  <TableCell>
                    <Badge className={roleColors[employee.role]}>
                      {roleLabels[employee.role]}
                    </Badge>
                  </TableCell>
                  <TableCell className="capitalize">{employee.department}</TableCell>
                  <TableCell>
                    {employee.userId ? (
                      <Badge variant="default" className="flex items-center space-x-1">
                        <CheckCircle className="h-3 w-3" />
                        <span>Sudah ada akun</span>
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="flex items-center space-x-1">
                        <AlertCircle className="h-3 w-3" />
                        <span>Belum ada akun</span>
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {!employee.userId && (
                      <Dialog open={isDialogOpen && selectedEmployee?.id === employee.id} onOpenChange={(open) => {
                        setIsDialogOpen(open);
                        if (!open) {
                          setSelectedEmployee(null);
                          setPassword('');
                          setSelectedRole('nurse');
                        }
                      }}>
                        <DialogTrigger asChild>
                          <Button
                            size="sm"
                            onClick={() => {
                              setSelectedEmployee(employee);
                              setSelectedRole(employee.role);
                            }}
                          >
                            <UserPlus className="w-4 h-4 mr-2" />
                            Buat Akun
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>
                              Buat Akun untuk {employee.firstName} {employee.lastName}
                            </DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label>Email</Label>
                              <div className="flex items-center space-x-2 mt-1">
                                <Mail className="w-4 h-4 text-gray-400" />
                                <span className="text-sm">{employee.email}</span>
                              </div>
                            </div>
                            <div>
                              <Label>Role</Label>
                              <div className="mt-1 space-y-2">
                                <Select value={selectedRole} onValueChange={(value: EmployeeRole) => setSelectedRole(value)}>
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {Object.entries(roleLabels).map(([role, label]) => (
                                      <SelectItem key={role} value={role}>
                                        <div className="flex items-center space-x-2">
                                          <Shield className="w-4 h-4" />
                                          <span>{label}</span>
                                        </div>
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <Badge className={roleColors[selectedRole]}>
                                  {roleLabels[selectedRole]}
                                </Badge>
                              </div>
                            </div>
                            <div>
                              <Label htmlFor="password">Password Sementara</Label>
                              <Input
                                id="password"
                                type="password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                                placeholder="Masukkan password sementara (min. 6 karakter)"
                                required
                                minLength={6}
                              />
                              <p className="text-xs text-gray-500 mt-1">
                                Password harus minimal 6 karakter
                              </p>
                            </div>
                            <div className="bg-blue-50 p-3 rounded-lg">
                              <p className="text-xs text-blue-800">
                                <strong>Catatan:</strong> Akun akan dibuat dengan role yang dipilih. 
                                Karyawan disarankan untuk mengganti password setelah login pertama.
                              </p>
                            </div>
                            <Button
                              onClick={() => handleCreateAccount(employee)}
                              disabled={loading || !password || password.length < 6}
                              className="w-full"
                            >
                              {loading ? 'Membuat Akun...' : `Buat Akun dengan Role ${roleLabels[selectedRole]}`}
                            </Button>
                          </div>
                        </DialogContent>
                      </Dialog>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
