// Schedules hook using API endpoints
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { scheduleApi } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

export function useSchedules(params?: {
  employeeId?: string;
  startDate?: string;
  endDate?: string;
}) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get all schedules
  const { data: schedulesData, isLoading, error } = useQuery({
    queryKey: ['schedules', params],
    queryFn: () => scheduleApi.getAll(params),
  });

  const schedules = schedulesData?.schedules || [];

  // Create schedule mutation
  const createScheduleMutation = useMutation({
    mutationFn: async (scheduleData: any) => {
      const response = await scheduleApi.create(scheduleData);
      return response.schedule;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      toast({
        title: "Berhasil",
        description: "Jadwal berhasil ditambahkan",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menambahkan jadwal",
        variant: "destructive",
      });
    },
  });

  // Update schedule mutation
  const updateScheduleMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const response = await scheduleApi.update(id, data);
      return response.schedule;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      toast({
        title: "Berhasil",
        description: "Jadwal berhasil diperbarui",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal memperbarui jadwal",
        variant: "destructive",
      });
    },
  });

  // Delete schedule mutation
  const deleteScheduleMutation = useMutation({
    mutationFn: async (id: string) => {
      await scheduleApi.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schedules'] });
      toast({
        title: "Berhasil",
        description: "Jadwal berhasil dihapus",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.error || "Gagal menghapus jadwal",
        variant: "destructive",
      });
    },
  });

  return {
    schedules,
    isLoading,
    error,
    createSchedule: createScheduleMutation.mutate,
    updateSchedule: updateScheduleMutation.mutate,
    deleteSchedule: deleteScheduleMutation.mutate,
    isCreating: createScheduleMutation.isPending,
    isUpdating: updateScheduleMutation.isPending,
    isDeleting: deleteScheduleMutation.isPending,
  };
}

// Hook for getting a single schedule
export function useSchedule(id: string) {
  return useQuery({
    queryKey: ['schedule', id],
    queryFn: async () => {
      const response = await scheduleApi.getById(id);
      return response.schedule;
    },
    enabled: !!id,
  });
}

// Hook for schedule statistics
export function useScheduleStatistics(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return useQuery({
    queryKey: ['schedule-statistics', params],
    queryFn: async () => {
      return await scheduleApi.getStatistics(params);
    },
  });
}
