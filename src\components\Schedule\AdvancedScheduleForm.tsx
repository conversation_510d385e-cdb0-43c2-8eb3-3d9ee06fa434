
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Users } from 'lucide-react';
import { useSchedules } from '@/hooks/useSchedules';
import { useEmployees } from '@/hooks/useEmployees';

const scheduleSchema = z.object({
  employeeIds: z.array(z.string()).min(1, 'Pilih minimal satu karyawan'),
  startDate: z.string().min(1, 'Tanggal mulai wajib diisi'),
  endDate: z.string().min(1, 'Tanggal selesai wajib diisi'),
  shiftType: z.enum(['morning', 'afternoon', 'night', 'rotating', 'regular']),
  customStartTime: z.string().optional(),
  customEndTime: z.string().optional(),
  notes: z.string().optional(),
  skipWeekends: z.boolean().default(false),
});

type ScheduleFormData = z.infer<typeof scheduleSchema>;

interface AdvancedScheduleFormProps {
  onSuccess?: () => void;
}

export function AdvancedScheduleForm({ onSuccess }: AdvancedScheduleFormProps) {
  const { createSchedule, isCreatingSchedule } = useSchedules();
  const { employees } = useEmployees();
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
  
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset,
  } = useForm<ScheduleFormData>({
    resolver: zodResolver(scheduleSchema),
    defaultValues: {
      skipWeekends: false,
    }
  });

  const shiftType = watch('shiftType');
  const startDate = watch('startDate');
  const endDate = watch('endDate');

  const getShiftTimes = (type: string, date?: Date) => {
    const dayOfWeek = date ? date.getDay() : 0; // 0 = Sunday, 6 = Saturday
    
    switch (type) {
      case 'morning':
        return { start: '07:00', end: '15:00' };
      case 'afternoon':
        return { start: '15:00', end: '23:00' };
      case 'night':
        return { start: '23:00', end: '07:00' };
      case 'regular':
        // Saturday: 08:00-13:00, Monday-Friday: 08:00-16:00
        return dayOfWeek === 6 
          ? { start: '08:00', end: '13:00' } 
          : { start: '08:00', end: '16:00' };
      default:
        return { start: '', end: '' };
    }
  };

  const handleShiftTypeChange = (value: string) => {
    setValue('shiftType', value as any);
    
    if (value !== 'rotating') {
      const times = getShiftTimes(value);
      setValue('customStartTime', times.start);
      setValue('customEndTime', times.end);
    }
  };

  const handleEmployeeSelect = (employeeId: string) => {
    const newSelection = selectedEmployees.includes(employeeId)
      ? selectedEmployees.filter(id => id !== employeeId)
      : [...selectedEmployees, employeeId];
    
    setSelectedEmployees(newSelection);
    setValue('employeeIds', newSelection);
  };

  const generateScheduleDates = (start: string, end: string, skipWeekends: boolean) => {
    const dates = [];
    const startDate = new Date(start);
    const endDate = new Date(end);
    
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dayOfWeek = d.getDay();
      
      // Skip Sunday (0) and Saturday (6) if skipWeekends is true
      // For regular shift, only skip Sunday
      if (shiftType === 'regular') {
        if (dayOfWeek === 0) continue; // Skip Sunday only
      } else if (skipWeekends && (dayOfWeek === 0 || dayOfWeek === 6)) {
        continue;
      }
      
      dates.push(new Date(d));
    }
    
    return dates;
  };

  const onSubmit = async (data: ScheduleFormData) => {
    const dates = generateScheduleDates(data.startDate, data.endDate, data.skipWeekends);
    
    for (const employeeId of data.employeeIds) {
      for (const date of dates) {
        const times = getShiftTimes(data.shiftType, date);
        const startTime = data.customStartTime || times.start;
        const endTime = data.customEndTime || times.end;
        
        const scheduleData = {
          employeeId,
          shiftDate: date.toISOString().split('T')[0],
          shiftType: data.shiftType,
          startTime,
          endTime,
          notes: data.notes,
          status: 'scheduled' as const,
        };
        
        createSchedule(scheduleData);
      }
    }
    
    reset();
    setSelectedEmployees([]);
    onSuccess?.();
  };

  const getShiftDescription = (type: string) => {
    switch (type) {
      case 'morning':
        return 'Pagi (07:00 - 15:00)';
      case 'afternoon':
        return 'Siang (15:00 - 23:00)';
      case 'night':
        return 'Malam (23:00 - 07:00)';
      case 'regular':
        return 'Reguler (Sen-Jum: 08:00-16:00, Sab: 08:00-13:00)';
      case 'rotating':
        return 'Bergilir (Waktu custom)';
      default:
        return '';
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Employee Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Pilih Karyawan
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-48 overflow-y-auto">
            {employees.map((employee) => (
              <div
                key={employee.id}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedEmployees.includes(employee.id)
                    ? 'bg-primary text-primary-foreground'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => handleEmployeeSelect(employee.id)}
              >
                <div className="font-medium">
                  {employee.firstName} {employee.lastName}
                </div>
                <div className="text-sm opacity-70">
                  {employee.department} - {employee.position}
                </div>
              </div>
            ))}
          </div>
          {selectedEmployees.length > 0 && (
            <div className="mt-3 flex flex-wrap gap-1">
              {selectedEmployees.map((employeeId) => {
                const employee = employees.find(e => e.id === employeeId);
                return employee ? (
                  <Badge key={employeeId} variant="secondary">
                    {employee.firstName} {employee.lastName}
                  </Badge>
                ) : null;
              })}
            </div>
          )}
          {errors.employeeIds && (
            <p className="text-sm text-red-600 mt-2">{errors.employeeIds.message}</p>
          )}
        </CardContent>
      </Card>

      {/* Date Range */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Periode Jadwal
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Tanggal Mulai *</Label>
              <Input
                id="startDate"
                type="date"
                {...register('startDate')}
              />
              {errors.startDate && (
                <p className="text-sm text-red-600">{errors.startDate.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">Tanggal Selesai *</Label>
              <Input
                id="endDate"
                type="date"
                {...register('endDate')}
              />
              {errors.endDate && (
                <p className="text-sm text-red-600">{errors.endDate.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Shift Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Konfigurasi Shift
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="shiftType">Tipe Shift *</Label>
            <Select onValueChange={handleShiftTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih tipe shift" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="morning">Pagi (07:00 - 15:00)</SelectItem>
                <SelectItem value="afternoon">Siang (15:00 - 23:00)</SelectItem>
                <SelectItem value="night">Malam (23:00 - 07:00)</SelectItem>
                <SelectItem value="regular">Reguler (Sen-Jum: 08:00-16:00, Sab: 08:00-13:00)</SelectItem>
                <SelectItem value="rotating">Bergilir (Waktu Custom)</SelectItem>
              </SelectContent>
            </Select>
            {errors.shiftType && (
              <p className="text-sm text-red-600">{errors.shiftType.message}</p>
            )}
          </div>

          {shiftType && (
            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm font-medium text-blue-800">
                {getShiftDescription(shiftType)}
              </p>
              {shiftType === 'regular' && (
                <p className="text-xs text-blue-600 mt-1">
                  Jadwal akan otomatis disesuaikan: Senin-Jumat (08:00-16:00), Sabtu (08:00-13:00), Minggu libur
                </p>
              )}
            </div>
          )}

          {(shiftType === 'rotating' || shiftType) && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="customStartTime">Jam Mulai {shiftType === 'rotating' ? '*' : '(Opsional)'}</Label>
                <Input
                  id="customStartTime"
                  type="time"
                  {...register('customStartTime')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="customEndTime">Jam Selesai {shiftType === 'rotating' ? '*' : '(Opsional)'}</Label>
                <Input
                  id="customEndTime"
                  type="time"
                  {...register('customEndTime')}
                />
              </div>
            </div>
          )}

          {shiftType !== 'regular' && (
            <div className="flex items-center space-x-2">
              <input
                id="skipWeekends"
                type="checkbox"
                {...register('skipWeekends')}
                className="rounded"
              />
              <Label htmlFor="skipWeekends" className="text-sm">
                Lewati akhir pekan (Sabtu & Minggu)
              </Label>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Additional Notes */}
      <div className="space-y-2">
        <Label htmlFor="notes">Catatan Tambahan</Label>
        <Textarea
          id="notes"
          placeholder="Catatan tambahan untuk jadwal ini (opsional)"
          {...register('notes')}
        />
      </div>

      {/* Preview */}
      {startDate && endDate && shiftType && selectedEmployees.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Pratinjau Jadwal</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>Karyawan:</strong> {selectedEmployees.length} orang dipilih</p>
              <p><strong>Periode:</strong> {startDate} sampai {endDate}</p>
              <p><strong>Tipe Shift:</strong> {getShiftDescription(shiftType)}</p>
              <p><strong>Perkiraan Jadwal:</strong> {generateScheduleDates(startDate, endDate, watch('skipWeekends')).length} hari kerja per karyawan</p>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-end space-x-2">
        <Button 
          type="button" 
          variant="outline" 
          onClick={() => {
            reset();
            setSelectedEmployees([]);
          }}
        >
          Reset
        </Button>
        <Button type="submit" disabled={isCreatingSchedule}>
          {isCreatingSchedule ? 'Membuat Jadwal...' : 'Buat Jadwal'}
        </Button>
      </div>
    </form>
  );
}
