
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { EmployeeRole } from '@/types/employee';

interface UserProfile {
  role: EmployeeRole;
  first_name: string;
  last_name: string;
}

export function useRoleAccess() {
  const { user } = useAuth();

  const { data: profile, isLoading } = useQuery({
    queryKey: ['profile', user?.id],
    queryFn: async () => {
      if (!user) return null;
      
      const { data, error } = await supabase
        .from('profiles')
        .select('role, first_name, last_name')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      return data as UserProfile;
    },
    enabled: !!user,
  });

  const hasRole = (requiredRole: EmployeeRole): boolean => {
    return profile?.role === requiredRole;
  };

  const hasAnyRole = (requiredRoles: EmployeeRole[]): boolean => {
    return requiredRoles.includes(profile?.role as Employee<PERSON>ole);
  };

  const isAdmin = (): boolean => {
    return profile?.role === 'admin';
  };

  const canAccessManagement = (): boolean => {
    return hasAnyRole(['admin', 'manager']);
  };

  const canManageEmployees = (): boolean => {
    return hasAnyRole(['admin', 'manager']);
  };

  const canViewReports = (): boolean => {
    return hasAnyRole(['admin', 'manager', 'doctor']);
  };

  return {
    profile,
    isLoading,
    hasRole,
    hasAnyRole,
    isAdmin,
    canAccessManagement,
    canManageEmployees,
    canViewReports,
    userRole: profile?.role,
  };
}
